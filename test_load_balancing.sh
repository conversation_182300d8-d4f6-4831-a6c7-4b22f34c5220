#!/bin/bash

# ===================================================================
# 负载均衡测试脚本
# 测试IPv4和IPv6是否真的在轮流负载均衡
# ===================================================================

# 配置参数
PROXY_HOST="***************"
PROXY_PORT="18888"
PROXY_USER="proxy"
PROXY_PASS="Yyh275822746Xyz"
PROXY_URL="http://${PROXY_USER}:${PROXY_PASS}@${PROXY_HOST}:${PROXY_PORT}"

# 测试的IP检测网站列表
IP_TEST_SITES=(
    "http://ip.sb"
    "http://ipinfo.io/ip"
    "http://icanhazip.com"
    "http://ident.me"
    "http://ifconfig.me"
    "http://httpbin.org/ip"
    "http://checkip.amazonaws.com"
    "http://whatismyipaddress.com/api/ip"
)

echo "=========================================="
echo "🧪 负载均衡测试"
echo "=========================================="
echo "代理地址: ${PROXY_HOST}:${PROXY_PORT}"
echo "测试网站数量: ${#IP_TEST_SITES[@]}"
echo ""

# 存储结果
declare -a results
declare -a ip_types
test_count=0
ipv4_count=0
ipv6_count=0

echo "开始测试负载均衡..."
echo ""

for site in "${IP_TEST_SITES[@]}"; do
    test_count=$((test_count + 1))
    echo "[$test_count/${#IP_TEST_SITES[@]}] 测试网站: $site"
    
    # 发起请求
    response=$(curl -x "$PROXY_URL" -s --connect-timeout 10 --max-time 15 "$site" 2>/dev/null)
    
    if [ -n "$response" ]; then
        # 清理响应（移除多余的空白字符和JSON格式）
        clean_ip=$(echo "$response" | grep -oE '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}|[0-9a-fA-F:]+' | head -1)
        
        if [ -n "$clean_ip" ]; then
            # 判断IP类型
            if [[ $clean_ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
                ip_type="IPv4"
                ipv4_count=$((ipv4_count + 1))
            elif [[ $clean_ip =~ ^[0-9a-fA-F:]+$ ]]; then
                ip_type="IPv6"
                ipv6_count=$((ipv6_count + 1))
            else
                ip_type="未知"
            fi
            
            results+=("$clean_ip")
            ip_types+=("$ip_type")
            
            echo "  ✅ 成功: $clean_ip ($ip_type)"
        else
            echo "  ❌ 响应格式无法解析: $response"
            results+=("解析失败")
            ip_types+=("未知")
        fi
    else
        echo "  ❌ 请求失败或超时"
        results+=("请求失败")
        ip_types+=("未知")
    fi
    
    # 短暂延迟，让负载均衡有时间切换
    sleep 1
done

echo ""
echo "=========================================="
echo "📊 负载均衡测试结果"
echo "=========================================="

echo "总请求数: $test_count"
echo "IPv4响应数: $ipv4_count"
echo "IPv6响应数: $ipv6_count"
echo ""

# 显示详细结果
echo "详细结果:"
for i in "${!results[@]}"; do
    printf "%2d. %-20s %s\n" $((i+1)) "${ip_types[i]}" "${results[i]}"
done

echo ""

# 分析负载均衡效果
echo "=========================================="
echo "🔍 负载均衡分析"
echo "=========================================="

if [ $ipv4_count -gt 0 ] && [ $ipv6_count -gt 0 ]; then
    echo "✅ 负载均衡工作正常！"
    echo "   - 检测到IPv4和IPv6交替使用"
    echo "   - IPv4使用率: $(( ipv4_count * 100 / test_count ))%"
    echo "   - IPv6使用率: $(( ipv6_count * 100 / test_count ))%"
    
    # 检查是否真的在交替
    alternating=true
    for i in $(seq 1 $((${#ip_types[@]} - 1))); do
        if [ "${ip_types[i]}" = "${ip_types[$((i-1))]}" ] && [ "${ip_types[i]}" != "未知" ]; then
            alternating=false
            break
        fi
    done
    
    if [ "$alternating" = true ]; then
        echo "✅ 完美交替：IPv4和IPv6严格交替使用"
    else
        echo "⚠️  部分交替：大部分情况下在交替使用"
    fi
    
elif [ $ipv4_count -gt 0 ] && [ $ipv6_count -eq 0 ]; then
    echo "ℹ️  仅使用IPv4"
    echo "   - 可能IPv6未启用或IPv6地址不可用"
    echo "   - 或者测试网站不支持IPv6"
    
elif [ $ipv4_count -eq 0 ] && [ $ipv6_count -gt 0 ]; then
    echo "ℹ️  仅使用IPv6"
    echo "   - 可能IPv4地址不可用"
    
else
    echo "❌ 负载均衡可能存在问题"
    echo "   - 未检测到有效的IP响应"
fi

# 检查唯一IP数量
unique_ips=($(printf '%s\n' "${results[@]}" | grep -v "请求失败\|解析失败" | sort -u))
echo ""
echo "检测到的唯一IP数量: ${#unique_ips[@]}"
for ip in "${unique_ips[@]}"; do
    echo "  - $ip"
done

echo ""
echo "=========================================="
echo "🎉 负载均衡测试完成"
echo "=========================================="