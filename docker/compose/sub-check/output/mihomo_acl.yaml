# DNS 配置
dns:
  enable: true
  listen: 0.0.0.0:1053
  # ipv6: true  # 根据需要取消注释
  prefer-h3: false
  respect-rules: true
  use-system-hosts: false
  cache-algorithm: arc
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  fake-ip-filter:
    - '+.lan'
    - '+.local'
    - '+.msftconnecttest.com'
    - '+.msftncsi.com'
    - 'localhost.ptlogin2.qq.com'
    - 'localhost.sec.qq.com'
    - 'localhost.work.weixin.qq.com'
  default-nameserver:
    - *********
    - *******
  nameserver:
    - https://cloudflare-dns.com/dns-query # CloudflareDNS
    - https://*********/dns-query # YandexDNS
    - 'https://*******/dns-query#ecs=*******/24&ecs-override=true' # GoogleDNS (带 ECS)
    - 'https://**************/dns-query#ecs=*******/24&ecs-override=true' # OpenDNS (带 ECS)
  proxy-server-nameserver:
    - https://*********/dns-query # 阿里DoH
    - https://doh.pub/dns-query # 腾讯DoH
  direct-nameserver:
    - https://*********/dns-query # 阿里DoH
    - https://doh.pub/dns-query # 腾讯DoH
  direct-nameserver-follow-policy: false
  nameserver-policy:
    'geosite:cn':
      - https://*********/dns-query # 阿里DoH
      - https://doh.pub/dns-query # 腾讯DoH

# 规则提供者配置
rule-providers:
  reject:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt
    path: ./ruleset/loyalsoldier/reject.yaml
  icloud:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt
    path: ./ruleset/loyalsoldier/icloud.yaml
  apple:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt
    path: ./ruleset/loyalsoldier/apple.yaml
  google:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt
    path: ./ruleset/loyalsoldier/google.yaml
  proxy:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt
    path: ./ruleset/loyalsoldier/proxy.yaml
  direct:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt
    path: ./ruleset/loyalsoldier/direct.yaml
  private:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt
    path: ./ruleset/loyalsoldier/private.yaml
  gfw:
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt
    path: ./ruleset/loyalsoldier/gfw.yaml
  'tld-not-cn': # 引号用于确保 key 正确解析
    type: http
    format: yaml
    interval: 86400
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt
    path: ./ruleset/loyalsoldier/tld-not-cn.yaml
  telegramcidr:
    type: http
    format: yaml
    interval: 86400
    behavior: ipcidr
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt
    path: ./ruleset/loyalsoldier/telegramcidr.yaml
  cncidr:
    type: http
    format: yaml
    interval: 86400
    behavior: ipcidr
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt
    path: ./ruleset/loyalsoldier/cncidr.yaml
  lancidr:
    type: http
    format: yaml
    interval: 86400
    behavior: ipcidr
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt
    path: ./ruleset/loyalsoldier/lancidr.yaml
  applications:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt
    path: ./ruleset/loyalsoldier/applications.yaml
  openai:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/openai.yaml
    path: ./ruleset/MetaCubeX/openai.yaml
  bybit:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/bybit.yaml
    path: ./ruleset/MetaCubeX/bybit.yaml
  pikpak:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/pikpak.yaml
    path: ./ruleset/MetaCubeX/pikpak.yaml
  anthropic:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/anthropic.yaml
    path: ./ruleset/MetaCubeX/anthropic.yaml
  'google-gemini': # 引号用于确保 key 正确解析
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/google-gemini.yaml
    path: ./ruleset/MetaCubeX/google-gemini.yaml
  xai:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/xai.yaml
    path: ./ruleset/MetaCubeX/xai.yaml
  perplexity:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/perplexity.yaml
    path: ./ruleset/MetaCubeX/perplexity.yaml
  microsoft:
    type: http
    format: yaml
    interval: 86400
    behavior: classical
    url: https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/microsoft.yaml
    path: ./ruleset/MetaCubeX/microsoft.yaml

# 规则
rules:
  # 额外自定义规则
  - PROCESS-NAME,steam.exe,🐬 自定义直连
  - DOMAIN-SUFFIX,immersivetranslate.com,🐳 自定义代理
  # - DOMAIN-SUFFIX,bing.com,🐳 自定义代理
  # 自定义规则
  - DOMAIN-SUFFIX,googleapis.cn,🔰 模式选择
  - DOMAIN-SUFFIX,gstatic.com,🔰 模式选择
  - DOMAIN-SUFFIX,xn--ngstr-lra8j.com,🔰 模式选择
  - DOMAIN-SUFFIX,github.io,🔰 模式选择
  - DOMAIN,v2rayse.com,🔰 模式选择
  # blackmatrix7 规则集 (脚本中未启用, 这里保留注释)
  # MetaCubeX 规则集
  - RULE-SET,openai,💸 ChatGPT-Gemini-XAI-Perplexity
  - RULE-SET,pikpak,🅿️ PikPak
  - RULE-SET,bybit,🪙 Bybit
  - RULE-SET,anthropic,💵 Claude
  - RULE-SET,google-gemini,💸 ChatGPT-Gemini-XAI-Perplexity
  - RULE-SET,xai,💸 ChatGPT-Gemini-XAI-Perplexity
  - RULE-SET,perplexity,💸 ChatGPT-Gemini-XAI-Perplexity
  # Loyalsoldier 规则集
  - RULE-SET,applications,🔗 全局直连
  - RULE-SET,private,🔗 全局直连
  - RULE-SET,reject,🥰 广告过滤
  - RULE-SET,microsoft,Ⓜ️ 微软服务
  - RULE-SET,icloud,🍎 苹果服务
  - RULE-SET,apple,🍎 苹果服务
  - RULE-SET,google,📢 谷歌服务
  - RULE-SET,proxy,🔰 模式选择
  - RULE-SET,gfw,🔰 模式选择
  - RULE-SET,tld-not-cn,🔰 模式选择
  - RULE-SET,direct,🔗 全局直连
  - RULE-SET,lancidr,🔗 全局直连,no-resolve
  - RULE-SET,cncidr,🔗 全局直连,no-resolve
  - RULE-SET,telegramcidr,📲 电报消息,no-resolve
  # 其他规则
  - GEOIP,LAN,🔗 全局直连,no-resolve
  - GEOIP,CN,🔗 全局直连,no-resolve
  - MATCH,🐟 漏网之鱼



# 代理组配置
# 注意: 脚本逻辑已为 "⚙️ 节点选择", "♻️ 延迟选优", "⚖️ 负载均衡(散列)", "☁️ 负载均衡(轮询)" 组
#       自动添加了 exclude-filter 以排除名为 "webshare" 的落地节点。
#       如果你的落地节点名字不同或有更多，你需要手动更新下面的 exclude-filter 正则表达式。
proxy-groups:
  - name: 🔰 模式选择
    type: select
    proxies:
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - 🔗 全局直连
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: ⚙️ 节点选择
    type: select
    proxies:
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
      # - DIRECT # 根据需要可以取消注释DIRECT
    use: # 引用所有 proxy-providers
      - p1
      - p2 # 如果有其他订阅，在这里添加
    # include-all: true # include-all 与 use 互斥，优先使用 use 指定 provider
    exclude-filter: '^(?:webshare)$' # 排除落地节点 'webshare' (根据脚本逻辑添加)
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/adjust.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🕊️ 落地节点
    type: select
    proxies:
      - webshare # 手动列出落地节点名称
      # - landing-node-2 # 如果有更多，在此添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/openwrt.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: ♻️ 延迟选优
    type: url-test
    tolerance: 50
    use:
      - p1
      - p2
    exclude-filter: '^(?:webshare)$' # 排除落地节点 (根据脚本逻辑添加)
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/speed.svg
    # --- groupBaseOption 展开 ---
    interval: 0 # url-test 通常有自己的测试间隔，这里 base 的 interval 设为 0
    timeout: 3000
    url: https://www.google.com/generate_204 # url-test 的测试地址
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🚑 故障转移
    type: fallback
    use:
      - p1
      - p2
    # exclude-filter: '^(?:webshare)$' # Fallback 通常不需要排除落地节点，除非你有特殊需求
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/ambulance.svg
    # --- groupBaseOption 展开 ---
    interval: 0 # fallback 通常有自己的测试间隔，这里 base 的 interval 设为 0
    timeout: 3000
    url: https://www.google.com/generate_204 # fallback 的测试地址
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: ⚖️ 负载均衡(散列)
    type: load-balance
    strategy: consistent-hashing
    use:
      - p1
      - p2
    exclude-filter: '^(?:webshare)$' # 排除落地节点 (根据脚本逻辑添加)
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/merry_go.svg
    # --- groupBaseOption 展开 ---
    interval: 0 # load-balance 通常有自己的健康检查间隔
    timeout: 3000
    url: https://www.google.com/generate_204 # load-balance 的健康检查地址
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: ☁️ 负载均衡(轮询)
    type: load-balance
    strategy: round-robin
    use:
      - p1
      - p2
    exclude-filter: '^(?:webshare)$' # 排除落地节点 (根据脚本逻辑添加)
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/balance.svg
    # --- groupBaseOption 展开 ---
    interval: 0 # load-balance 通常有自己的健康检查间隔
    timeout: 3000
    url: https://www.google.com/generate_204 # load-balance 的健康检查地址
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🌍 国外媒体
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
      - 🔗 全局直连
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/youtube.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 💸 ChatGPT-Gemini-XAI-Perplexity
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - 🔗 全局直连
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
    use:
      - p1
      - p2
    exclude-filter: "(?i)港|hk|hongkong|hong kong|俄|ru|russia|澳|macao" # 脚本自带的过滤器
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 💵 Claude
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - 🔗 全局直连
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/claude.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🪙 Bybit
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - 🔗 全局直连
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg # 图标可能需要调整
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🅿️ PikPak
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - 🔗 全局直连
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg # 图标可能需要调整
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 📲 电报消息
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
      - 🔗 全局直连
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/telegram.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 📢 谷歌服务
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
      - 🔗 全局直连
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/google.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🍎 苹果服务
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
      - 🔗 全局直连
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/apple.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: Ⓜ️ 微软服务
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - 🔗 全局直连
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/microsoft.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🥰 广告过滤
    type: select
    proxies:
      - REJECT
      - DIRECT
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/bug.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🔗 全局直连
    type: select
    proxies:
      - DIRECT
      - ⚙️ 节点选择
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
    # use: # 通常直连组不需要 use provider
    # include-all: true # 如果你想包含所有独立定义的代理节点，可以取消注释
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: ❌ 全局拦截
    type: select
    proxies:
      - REJECT
      - DIRECT
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/block.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🐬 自定义直连
    type: select
    proxies:
      - 🔗 全局直连
      - 🔰 模式选择
      - ⚙️ 节点选择
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
    # use: # 根据需要添加
    # include-all: true # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/unknown.svg # 图标可能需要调整
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🐳 自定义代理
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
      - 🔗 全局直连
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/openwrt.svg # 图标可能需要调整
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false

  - name: 🐟 漏网之鱼
    type: select
    proxies:
      - 🔰 模式选择
      - ⚙️ 节点选择
      - 🕊️ 落地节点
      - ♻️ 延迟选优
      - 🚑 故障转移
      - ⚖️ 负载均衡(散列)
      - ☁️ 负载均衡(轮询)
      - 🔗 全局直连
    use:
      - p1
      - p2
    # exclude-filter: # 根据需要添加
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/fish.svg
    # --- groupBaseOption 展开 ---
    interval: 0
    timeout: 3000
    url: https://www.google.com/generate_204
    lazy: true
    max-failed-times: 3
    hidden: false



