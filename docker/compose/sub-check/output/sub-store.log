
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: Start migrating...
[sub-store] INFO: Migration complete!
[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] 127.0.0.1:8299
[sub-store] INFO: [BACKEND PREFIX] 127.0.0.1:8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on 127.0.0.1:8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在创建订阅： sub-check
[sub-store] INFO: 正在下载订阅：sub-check
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] INFO: port is not present in line: http://127.0.0.1:8199/all.yaml, set to 80
[sub-store] INFO: URI PROXY Parser is activated
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] INFO: port is not present in line: http://127.0.0.1:8199/all.yaml, set to 80
[sub-store] INFO: URI PROXY Parser is activated
[sub-store] INFO: 正在更新订阅： sub-check
[sub-store] INFO: 正在下载订阅：sub-check
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8199/all.yaml
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误: Error: 无法下载 URL http://127.0.0.1:8199/all.yaml: statusCode: 404
🌍 Sub-Store 下载订阅失败
❌ 无法下载订阅：sub-check！
🤔 原因：订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误, 请查看日志


[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误, 请查看日志
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8199/all.yaml
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误: Error: 无法下载 URL http://127.0.0.1:8199/all.yaml: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误, 请查看日志
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8199/all.yaml
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误: Error: 无法下载 URL http://127.0.0.1:8199/all.yaml: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误, 请查看日志
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8199/all.yaml
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误: Error: 无法下载 URL http://127.0.0.1:8199/all.yaml: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误, 请查看日志
[sub-store] INFO: 正在下载订阅：sub-check
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8199/all.yaml
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误: Error: 无法下载 URL http://127.0.0.1:8199/all.yaml: statusCode: 404
🌍 Sub-Store 下载订阅失败
❌ 无法下载订阅：sub-check！
🤔 原因：订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误, 请查看日志


[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8199/all.yaml 发生错误, 请查看日志
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 正在更新订阅： sub-check
[sub-store] INFO: 正在更新订阅： sub-check
[sub-store] INFO: 正在创建订阅： 自建节点
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: https://raw.githubusercontent.com/Keywos/rule/main/rename.js
[sub-store] INFO: statusCode: 200
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 正在下载订阅：sub-check
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash.meta
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误: Error: 无法下载 URL http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta: statusCode: 404
🌍 Sub-Store 下载订阅失败
❌ 无法下载订阅：sub-check！
🤔 原因：订阅 sub-check 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误, 请查看日志


[sub-store] ERROR: 订阅 sub-check 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误, 请查看日志
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 正在创建订阅： sub
[sub-store] INFO: 正在创建文件：mihomo
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash.meta
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 还原备份中...
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] INFO: perform migration after restoring from gist...
[sub-store] INFO: migration completed
[sub-store] INFO: 还原备份完成
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash.meta
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 subcheck 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误: Error: 无法下载 URL http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta: statusCode: 404
[sub-store] ERROR: 订阅 subcheck 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误, 请查看日志
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash.meta
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 subcheck 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误: Error: 无法下载 URL http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta: statusCode: 404
[sub-store] ERROR: 订阅 subcheck 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误, 请查看日志
[sub-store] INFO: 正在创建订阅： subcheck
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 正在下载订阅：subcheck
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: undefined
实际输出: JSON
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash.meta
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: statusCode: 404
[sub-store] ERROR: 订阅 subcheck 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误: Error: 无法下载 URL http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta: statusCode: 404
🌍 Sub-Store 下载订阅失败
❌ 无法下载订阅：subcheck！
🤔 原因：订阅 subcheck 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误, 请查看日志


[sub-store] ERROR: 订阅 subcheck 的远程订阅 http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta 发生错误, 请查看日志
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/download/sub?target=ClashMeta, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：sub！
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8199/sub/all.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: 正在更新订阅： subcheck
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载订阅：subcheck
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: undefined
实际输出: JSON
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/all.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/all.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: 正在更新订阅： subcheck
[sub-store] INFO: 正在下载订阅：subcheck
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/all.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在更新订阅： subcheck
[sub-store] INFO: 正在下载订阅：subcheck
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/all.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇺🇸US_279 |6.2MB/s Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 🇫🇮FI_20 |6.6MB/s Parsashonam-Bot-3
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: https://raw.githubusercontent.com/Keywos/rule/main/rename.js
[sub-store] INFO: statusCode: 200
[sub-store] ERROR: UUID may be invalid: 美国 279 Parsashonam-Bot-3
[sub-store] ERROR: UUID may be invalid: 芬兰 20 Parsashonam-Bot-3
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在创建订阅： sub
[sub-store] INFO: 正在创建文件：mihomo
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.74
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在下载订阅：subcheck
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: true
URL: http://127.0.0.1:8199/sub/all.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 使用 HEAD 方法从响应头获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 使用 GET 方法获取流量信息: http://127.0.0.1:8199/sub/all.yaml, User-Agent: clash, Insecure: false, Proxy: undefined
[sub-store] INFO: 正在创建文件：subcheck
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新文件：Mihomo...
[sub-store] INFO: 正在删除文件：Surge
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在删除文件：subcheck
[sub-store] INFO: 正在更新文件：本地自建...
[sub-store] INFO: 删除订阅：subcheck...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在更新文件：本地自建(mihomo)...
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在更新文件：Mihomo...
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: https://raw.githubusercontent.com/Keywos/rule/main/rename.js
[sub-store] INFO: statusCode: 200
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在更新文件：Mihomo...
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: undefined
实际输出: Clash
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Fallback Base64 Pre-processor] activated
[sub-store] ERROR: Fallback Base64 Pre-processor error: decoded line does not start with protocol
[sub-store] ERROR: Failed to parse line: # alicloud
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # crash-hk
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] ERROR: Failed to parse line: # bwg
[sub-store] INFO: URI VLESS Parser is activated
[sub-store] ERROR: Failed to parse line: # crashwork-us
[sub-store] INFO: URI AnyTLS Parser is activated
[sub-store] INFO: 使用缓存: https://raw.githubusercontent.com/Keywos/rule/main/rename.js, clash.meta
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 正在更新文件：Mihomo...
[sub-store] INFO: 下载备份, 与本地内容对比...
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] INFO: 上传备份中...
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] INFO: 上传备份完成
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/mihomo_acl.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/mihomo_acl.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在创建文件：subcheck
[sub-store] INFO: 正在更新文件：subcheck...
[sub-store] INFO: 正在下载文件：subcheck
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载文件：subcheck
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载文件：subcheck
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载文件：subcheck
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在更新文件：subcheck...
[sub-store] INFO: 正在下载文件：subcheck
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载文件：subcheck
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: clash.meta/v1.19.10
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在删除文件：subcheck
[sub-store] INFO: 下载备份, 与本地内容对比...
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] INFO: 上传备份中...
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] INFO: 上传备份完成
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.91
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 0 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在删除组合订阅：所有节点
[sub-store] INFO: 删除订阅：自建节点...
[sub-store] INFO: 正在删除文件：Mihomo
[sub-store] INFO: 正在删除文件：clash
[sub-store] INFO: 下载备份, 与本地内容对比...
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 0 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Auto Generated Sub-Store Backup)
[sub-store] INFO: 上传备份中...
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 0 个
[sub-store] INFO: 上传备份完成
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: clash.meta
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: clash.meta
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Shadowrocket/2615 CFNetwork/3826.500.131 Darwin/24.5.0 iPhone14,3
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Shadowrocket/2615 CFNetwork/3826.500.131 Darwin/24.5.0 iPhone14,3
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: clash.meta
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 获取到当前 GitHub 用户的 gist: 1 个
[sub-store] ERROR: 找不到 Sub-Store Gist (Sub-Store Artifacts Repository)
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：Mihomo
请求 User-Agent: mihomo.party/v1.7.4 (clash.meta)
[sub-store] ERROR: 🌍 Sub-Store 下载文件失败
❌ 未找到文件：Mihomo！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.91
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.91
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] MERGE mode is [ON].
[sub-store] INFO: [BACKEND && FRONTEND] :::8299
[sub-store] INFO: [BACKEND PREFIX] :::8299/suWjf72Afbnvd8234UbS
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在下载订阅：自建节点
请求 User-Agent: Clash
请求 target: ClashMeta
实际输出: ClashMeta
[sub-store] ERROR: 🌍 Sub-Store 下载订阅失败
❌ 未找到订阅：自建节点！
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: mihomo.party/v1.7.6 (clash.meta)
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
