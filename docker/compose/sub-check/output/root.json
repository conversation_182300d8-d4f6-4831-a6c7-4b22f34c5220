{"sub-store-cached-resource": "{\"15c409df76067619c94045ccd1afd33c\":{\"time\":1753420315706,\"data\":\"proxy-groups:\\n  - name: 🚀 节点选择\\n    type: select\\n    proxies:\\n      - ♻️ 自动选择\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 🚀 手动切换\\n    include-all: true\\n    type: select\\n  - name: ♻️ 自动选择\\n    type: url-test\\n    include-all: true\\n    interval: 300\\n    tolerance: 50\\n  - name: 📲 电报消息\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 💬 Ai平台\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 📹 油管视频\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 🎥 奈飞视频\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🎥 奈飞节点\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 📺 巴哈姆特\\n    type: select\\n    proxies:\\n      - 🇨🇳 台湾节点\\n      - 🚀 节点选择\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 📺 哔哩哔哩\\n    type: select\\n    proxies:\\n      - 🎯 全球直连\\n      - 🇨🇳 台湾节点\\n      - 🇭🇰 香港节点\\n  - name: 🌍 国外媒体\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 🌏 国内媒体\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🚀 手动切换\\n  - name: 📢 谷歌FCM\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: Ⓜ️ 微软Bing\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: Ⓜ️ 微软云盘\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: Ⓜ️ 微软服务\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - DIRECT\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🍎 苹果服务\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🎮 游戏平台\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🎶 网易音乐\\n    type: select\\n    include-all: true\\n    filter: (?i)网易|音乐|NetEase|Music\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n  - name: 🎯 全球直连\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n  - name: 🛑 广告拦截\\n    type: select\\n    proxies:\\n      - REJECT\\n      - DIRECT\\n  - name: 🍃 应用净化\\n    type: select\\n    proxies:\\n      - REJECT\\n      - DIRECT\\n  - name: 🐟 漏网之鱼\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - DIRECT\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🇭🇰 香港节点\\n    include-all: true\\n    filter: (?i)港|HK|hk|Hong Kong|HongKong|hongkong\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇯🇵 日本节点\\n    include-all: true\\n    filter: (?i)日本|川日|东京|大阪|泉日|埼玉|沪日|深日|JP|Japan\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇺🇲 美国节点\\n    include-all: true\\n    filter: (?i)美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇨🇳 台湾节点\\n    include-all: true\\n    filter: (?i)台|新北|彰化|TW|Taiwan\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇸🇬 狮城节点\\n    include-all: true\\n    filter: (?i)新加坡|坡|狮城|SG|Singapore\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇰🇷 韩国节点\\n    include-all: true\\n    filter: (?i)KR|Korea|KOR|首尔|韩|韓\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🎥 奈飞节点\\n    include-all: true\\n    filter: (?i)NF|奈飞|解锁|Netflix|NETFLIX|Media\\n    type: select\\n\\nrule-providers:\\n  LocalAreaNetwork:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/LocalAreaNetwork.list\\n    path: ./ruleset/LocalAreaNetwork.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  UnBan:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/UnBan.list\\n    path: ./ruleset/UnBan.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  BanAD:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanAD.list\\n    path: ./ruleset/BanAD.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  BanProgramAD:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanProgramAD.list\\n    path: ./ruleset/BanProgramAD.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  GoogleFCM:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/GoogleFCM.list\\n    path: ./ruleset/GoogleFCM.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  GoogleCN:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/GoogleCN.list\\n    path: ./ruleset/GoogleCN.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  SteamCN:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/SteamCN.list\\n    path: ./ruleset/SteamCN.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Bing:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Bing.list\\n    path: ./ruleset/Bing.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  OneDrive:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/OneDrive.list\\n    path: ./ruleset/OneDrive.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Microsoft:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Microsoft.list\\n    path: ./ruleset/Microsoft.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Apple:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Apple.list\\n    path: ./ruleset/Apple.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Telegram:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Telegram.list\\n    path: ./ruleset/Telegram.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  AI:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/AI.list\\n    path: ./ruleset/AI.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  OpenAi:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/OpenAi.list\\n    path: ./ruleset/OpenAi.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  NetEaseMusic:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/NetEaseMusic.list\\n    path: ./ruleset/NetEaseMusic.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Epic:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Epic.list\\n    path: ./ruleset/Epic.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Origin:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Origin.list\\n    path: ./ruleset/Origin.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Sony:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Sony.list\\n    path: ./ruleset/Sony.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Steam:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Steam.list\\n    path: ./ruleset/Steam.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Nintendo:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Nintendo.list\\n    path: ./ruleset/Nintendo.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  YouTube:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/YouTube.list\\n    path: ./ruleset/YouTube.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Netflix:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Netflix.list\\n    path: ./ruleset/Netflix.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Bahamut:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bahamut.list\\n    path: ./ruleset/Bahamut.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  BilibiliHMT:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/BilibiliHMT.list\\n    path: ./ruleset/BilibiliHMT.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Bilibili:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bilibili.list\\n    path: ./ruleset/Bilibili.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ChinaMedia:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaMedia.list\\n    path: ./ruleset/ChinaMedia.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ProxyMedia:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyMedia.list\\n    path: ./ruleset/ProxyMedia.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ProxyGFWlist:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyGFWlist.list\\n    path: ./ruleset/ProxyGFWlist.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ChinaDomain:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaDomain.list\\n    path: ./ruleset/ChinaDomain.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ChinaCompanyIp:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaCompanyIp.list\\n    path: ./ruleset/ChinaCompanyIp.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Download:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Download.list\\n    path: ./ruleset/Download.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n\\nrules:\\n  - \\\"PROCESS-NAME,subs-check.exe,🎯 全球直连\\\"\\n  - \\\"PROCESS-NAME,subs-check,🎯 全球直连\\\"\\n  - \\\"RULE-SET,LocalAreaNetwork,🎯 全球直连\\\"\\n  - \\\"RULE-SET,UnBan,🎯 全球直连\\\"\\n  - \\\"RULE-SET,BanAD,🛑 广告拦截\\\"\\n  - \\\"RULE-SET,BanProgramAD,🍃 应用净化\\\"\\n  - \\\"RULE-SET,GoogleFCM,📢 谷歌FCM\\\"\\n  - \\\"RULE-SET,GoogleCN,🎯 全球直连\\\"\\n  - \\\"RULE-SET,SteamCN,🎯 全球直连\\\"\\n  - \\\"RULE-SET,Bing,Ⓜ️ 微软Bing\\\"\\n  - \\\"RULE-SET,OneDrive,Ⓜ️ 微软云盘\\\"\\n  - \\\"RULE-SET,Microsoft,Ⓜ️ 微软服务\\\"\\n  - \\\"RULE-SET,Apple,🍎 苹果服务\\\"\\n  - \\\"RULE-SET,Telegram,📲 电报消息\\\"\\n  - \\\"RULE-SET,AI,💬 Ai平台\\\"\\n  - \\\"RULE-SET,NetEaseMusic,🎶 网易音乐\\\"\\n  - \\\"RULE-SET,Epic,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Origin,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Sony,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Steam,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Nintendo,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,YouTube,📹 油管视频\\\"\\n  - \\\"RULE-SET,Netflix,🎥 奈飞视频\\\"\\n  - \\\"RULE-SET,Bahamut,📺 巴哈姆特\\\"\\n  - \\\"RULE-SET,BilibiliHMT,📺 哔哩哔哩\\\"\\n  - \\\"RULE-SET,Bilibili,📺 哔哩哔哩\\\"\\n  - \\\"RULE-SET,ChinaMedia,🌏 国内媒体\\\"\\n  - \\\"RULE-SET,ProxyMedia,🌍 国外媒体\\\"\\n  - \\\"RULE-SET,ProxyGFWlist,🚀 节点选择\\\"\\n  - \\\"RULE-SET,ChinaDomain,🎯 全球直连\\\"\\n  - \\\"RULE-SET,ChinaCompanyIp,🎯 全球直连\\\"\\n  - \\\"RULE-SET,Download,🎯 全球直连\\\"\\n  - \\\"GEOIP,CN,🎯 全球直连\\\"\\n  - \\\"MATCH,🐟 漏网之鱼\\\"\\n\"}}", "sub-store-chr-expiration-time": "6e4", "sub-store-cached-headers-resource": "{}", "sub-store-csr-expiration-time": "1728e5", "sub-store-cached-script-resource": "{}", "sub-store": "{\n  \"subs\": [\n    {\n      \"name\": \"自建节点\",\n      \"displayName\": \"\",\n      \"form\": \"\",\n      \"remark\": \"\",\n      \"mergeSources\": \"\",\n      \"ignoreFailedRemoteSub\": false,\n      \"passThroughUA\": false,\n      \"icon\": \"\",\n      \"process\": [\n        {\n          \"type\": \"Quick Setting Operator\",\n          \"args\": {\n            \"useless\": \"DISABLED\",\n            \"udp\": \"DEFAULT\",\n            \"scert\": \"DEFAULT\",\n            \"tfo\": \"DEFAULT\",\n            \"vmess aead\": \"DEFAULT\"\n          }\n        },\n        {\n          \"type\": \"Script Operator\",\n          \"args\": {\n            \"content\": \"https://raw.githubusercontent.com/Keywos/rule/main/rename.js\",\n            \"mode\": \"link\",\n            \"arguments\": {}\n          },\n          \"id\": \"82582513.58029063\",\n          \"disabled\": false\n        }\n      ],\n      \"source\": \"local\",\n      \"url\": \"\",\n      \"content\": \"# alicloud\\nvless://YXV0bzo2ZmQ4Y2QyMy1lOTViLTRmNDgtYjg0Yi1lYzM1MTFkZGVlZDZANDcuMjQyLjI4LjE3MToxMDAwMQ==?remarks=HK1%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=xcCOhoZcbjtMf-S9AQi-ZPB_Oni8LG_lmQkfIDbagSk\\nanytls://6fd8cd23-e95b-4f48-b84b-ec3511ddeed6@*************:10002?insecure=1&udp=1#HK1%20anytls\\nvless://YXV0bzphMzg2YTFmMy03MzAxLTRhMzUtODhkYS1iNTcxMDFkNmJhZGJANDcuMjM5LjIzMC45NToxMDAwMQ==?remarks=HK22%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=EOngRDBf51f8-A0vEZgAXPpx3Vym18o4lrr2GGHexkg\\nanytls://a386a1f3-7301-4a35-88da-b57101d6badb@*************:10002?insecure=1&udp=1#HK22%20anytls\\nvless://YXV0bzo2MjUxYTkwNS04OWVmLTQ2ZWEtYjcyYy1kYmFjMmFlZGU0ZWVANDcuMjM5LjIyMi41MTo4ODgx?remarks=HK3%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=0_X0_AhIelZpJ6Iq11j1Hd3eUi3eEJTng9PhUaWx_w8\\nanytls://6251a905-89ef-46ea-b72c-dbac2aede4ee@*************:8882?insecure=1&udp=1#HK3%20anytls\\nvless://YXV0bzpjYmNmMzg5NS03MWUwLTRjYjYtYWEzYy1lZmNlNDg3YzJhMjNAOC4yMDkuMjA5Ljc2OjEwMDAx?remarks=JP12%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=Y3D7TWF6y4zC5XSoFb2DPgjjIsRhKvRkDxKvEVhTE2M\\nanytls://cbcf3895-71e0-4cb6-aa3c-efce487c2a23@************:10002?insecure=1&udp=1#JP12%20anytls\\nvless://YXV0bzpiMTNhZTNkMy03YzI3LTQ4NzItOGNkYy03NGJiNWRlYzk0NGFAOC4yMTMuMTI4Ljk4Ojg4ODE=?remarks=KR01%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=dM3c2HH-t7ur7LZ5OwoCoGQD1iQBCwxslt9lHPfkhgo\\nanytls://b13ae3d3-7c27-4872-8cdc-74bb5dec944a@************:8882?insecure=1&udp=1#KR01%20anytls\\n\\n# crash-hk\\nvless://YXV0bzoxNDY0ZWUxNi02YmZlLTQxZWYtYjk1ZC0xNDYxZjEwYWQ5OWNAMTU0LjIyMi4yOC45Mzo4ODgx?remarks=HK%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=4kr2tW1F9gh18HbHU8BiViSMotDErmvXzLdzg6wKy3s\\nanytls://1464ee16-6bfe-41ef-b95d-1461f10ad99c@*************:8882?insecure=1&udp=1#HK%20anytls\\nvless://YXV0bzoyYzZmMTM1ZC00YWJmLTRkZDItYTkwYi1lMGViZTZkMGFlMzFAMTU0LjIyMi4yOC4yMTo4ODgx?remarks=HK%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=jBgP3-qyOCMPMti9kCixcRO4YmEAItitil72urjF_X8\\nanytls://2c6f135d-4abf-4dd2-a90b-e0ebe6d0ae31@*************:8882?insecure=1&udp=1#HK%20anytls\\nvless://YXV0bzo1OTQ0NDk3Zi1lMTcwLTRjMjUtYjc1Mi0yYjFjN2UxM2Y1ZjhAMTAzLjEyMS45NC44Njo4ODgx?remarks=HK%20grpc-reality&path=grpc&obfs=grpc&tls=1&peer=addons.mozilla.org&pbk=bbo0l1PkOLejcdU2mNqUDWv9rV7Bl3SNHsIHRnLuahw\\nanytls://5944497f-e170-4c25-b752-2b1c7e13f5f8@*************:8882?insecure=1&udp=1#HK%20anytls\\n\\n\\n# bwg\\nvless://9893af12-8e36-414c-a2af-febc42fb24c6@*************:10000?encryption=none&security=reality&sni=addons.mozilla.org&fp=chrome&pbk=f1dfyPjrxsuf3GRbPPxjDAB1skKCj1jJRE5hGL12olY&type=grpc&serviceName=grpc&mode=gun#US11%20grpc-reality\\n# crashwork-us\\nvless://985058b5-545a-46e1-b59f-059f14eb529c@*************:6000?security=reality&sni=addons.mozilla.org&fp=chrome&pbk=XIRAWTtCIAeujLj3WjzaM8bDUxe3uUDaIhOcGHX4bEs&type=tcp&encryption=none#US2%20xtls-reality\\nvless://985058b5-545a-46e1-b59f-059f14eb529c@*************:6001?security=reality&sni=addons.mozilla.org&fp=chrome&pbk=XIRAWTtCIAeujLj3WjzaM8bDUxe3uUDaIhOcGHX4bEs&type=grpc&serviceName=grpc&encryption=none#US2%20grpc-reality\\nanytls://985058b5-545a-46e1-b59f-059f14eb529c@*************:6002/?insecure=1#US2%20anytls\",\n      \"ua\": \"\",\n      \"tag\": [],\n      \"subscriptionTags\": [],\n      \"display-name\": \"\",\n      \"isIconColor\": true\n    }\n  ],\n  \"collections\": [\n    {\n      \"name\": \"所有节点\",\n      \"displayName\": \"\",\n      \"form\": \"\",\n      \"remark\": \"\",\n      \"mergeSources\": \"\",\n      \"ignoreFailedRemoteSub\": false,\n      \"passThroughUA\": false,\n      \"icon\": \"\",\n      \"process\": [\n        {\n          \"type\": \"Quick Setting Operator\",\n          \"args\": {\n            \"useless\": \"DISABLED\",\n            \"udp\": \"DEFAULT\",\n            \"scert\": \"DEFAULT\",\n            \"tfo\": \"DEFAULT\",\n            \"vmess aead\": \"DEFAULT\"\n          }\n        }\n      ],\n      \"subscriptions\": [\n        \"自建节点\"\n      ],\n      \"tag\": [],\n      \"subscriptionTags\": [],\n      \"display-name\": \"\"\n    }\n  ],\n  \"artifacts\": [],\n  \"rules\": [],\n  \"files\": [\n    {\n      \"name\": \"Mihomo\",\n      \"displayName\": \"\",\n      \"remark\": \"\",\n      \"icon\": \"\",\n      \"source\": \"local\",\n      \"sourceType\": \"collection\",\n      \"sourceName\": \"\",\n      \"process\": [],\n      \"type\": \"file\",\n      \"content\": \"rule-anchor:\\n  # 订阅\\n  P: &P {type: http, interval: 86400, health-check: {enable: true, interval: 300}, header: {User-Agent:[\\\"Clash\\\", \\\"Clash.meta\\\",\\\"ClashMeta\\\"]}, exclude-filter:\\\"官网|剩余|流量|套餐|订阅|全球直连|GB|Expire Date|Traffic|ExpireDate\\\", proxy: DIRECT, override: {udp: true, down: \\\"200 Mbps\\\", up: \\\"100 Mbps\\\", skip-cert-verify: true}}\\n  # DNS\\n  dns_default: &dns_default [*********, ************]\\n  dns_cn: &dns_cn [\\\"https://doh.pub/dns-query\\\", \\\"https://dns.alidns.com/dns-query\\\"]\\n  dns_abroad: &dns_abroad [\\\"https://dns.google/dns-query\\\", \\\"https://cloudflare-dns.com/dns-query\\\"]\\n  dns_direct: &dns_direct [\\\"***********\\\"] # 需要根据实际修改\\n  \\n  # 节点选择\\n  S1: &S1 {type: select, proxies: [默认代理, 香港自动, 日本自动, 韩国自动, 美国自动, DIRECT]}\\t# 全部手动\\n  # 节点筛选\\n  HK: &HK \\\"(?i)港|hk|hongkong|hong kong\\\" #\\\"(?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|美)).)*$\\\"\\n  JP: &JP \\\"(?i)日本|jp|japan\\\" #\\\"(?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$\\\"\\n  US: &US \\\"(?i)美|us|unitedstates|united states\\\" #\\\"(?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$\\\"\\n  KR: &KR \\\"(?i)韩|kr|korea|south korea\\\" #\\\"(?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$\\\"\\n  Exclude: &Exclude \\\"^((?!(官网|剩余|流量|套餐|订阅|全球直连|GB|Expire Date|Traffic|ExpireDate)).)*$\\\"\\n  # 策略组\\n  SD: &SD {type: select, include-all: true}\\n  # fallback 将按照 url 测试结果按照节点顺序选择\\n  FB: &FB {type: fallback, include-all: true, interval: 60, lazy: true} # , url: \\\"http://www.apple.com/library/test/success.html\\\"\\n  # url-test 将按照 url 测试结果使用延迟最低节点\\n  URL: &URL {type: url-test, include-all: true, tolerance: 50, interval: 300, lazy: true}\\n  # load-balance 将按照算法随机选择节点\\n  LB: &LB {type: load-balance, include-all: true, interval: 300, strategy: round-robin}\\n  # 指定某个机场, use: [SubStore] <--> include-all: true  使用proxies中定义的全部节点\\n  # ICON\\n  Transfer: &Transfer \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Transfer.png\\\"\\n  Auto: &Auto \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Auto_Link.png\\\"\\n  All: &All \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Global.png\\\"\\n  Default: &Default \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Flight.png\\\"\\n  # 规则集\\n  # class: &class {type: file, interval: 864000, behavior: classical, format: yaml}\\n  ip: &ip {type: http, interval: 86400, behavior: ipcidr, format: mrs}\\n  domain: &domain {type: http, interval: 86400, behavior: domain, format: mrs}\\n  domain_text: &domain_text {type: http, interval: 86400, behavior: domain, format: text}\\t# list结尾的文件\\n  # mrs目前 behavior 仅支持 domain/ipcidr  可以通过mihomo convert-ruleset domain/ipcidr yaml/text XXX.yaml XXX.mrs转换得到\\n  \\nproxy-providers:\\n  SubStore: {<<: *P, url: \\\"https://sub.proxygo.de/suWjf72Afbnvd8234UbS/download/collection/%E6%89%80%E6%9C%89%E8%8A%82%E7%82%B9?target=ClashMeta\\\",override: {udp: true}}\\n  \\nproxies:\\n  # - {name: \\\"全球直连\\\", type: \\\"direct\\\"}\\n  # - {name: 链式代理,}\\n\\n# -----------------------------全局配置-----------------------------\\nport: 7890 # 仅HTTP协议专用端口（如果你的软件只支持HTTP代理，用这个）\\nsocks-port: 7891\\nredir-port: 7892 # 仅SOCKS5协议专用端口（如果你的软件只支持SOCKS5代理，用这个）\\nmixed-port: 7893\\t# 通用代理端口：同时支持HTTP和SOCKS5协议，一般软件都可以使用这个端口\\ntproxy-port: 7894\\nallow-lan: true # 是否允许来自局域网的连接（开启后，其他设备可以通过你的电脑上网）\\nbind-address: \\\"*\\\"\\t# 监听地址，\\\"*\\\"表示监听所有地址，这样局域网内其他设备才能连接\\nmode: rule # 默认就是rule\\nipv6: true\\t\\t# 内核是否支持ipv6≠能用\\nunified-delay: false\\t# 降低延迟,为了测试结果好看些\\nexperimental:\\n    ignore-resolve-fail: true\\ntcp-concurrent: true\\t# tcp并发连接,保持默认开启\\nlog-level: warning # 日志设置:silent=不显示日志,info=显示基本信息,warning=显示警告 error=只显示错误,debug=显示所有调试信息(日志会占用内存,一般保持静默即可)\\nfind-process-mode: off # 匹配进程 always/strict/off,路由器用的话off就行,因为没进程可以匹配。strict为内核自动匹配进程\\ncfw-latency-timeout: 5000\\ncfw-latency-url: 'http://www.apple.com/library/test/success.html'\\ncfw-conn-break-strategy: true # 启用后，当当前连接中断时，Clash会自动尝试使用其他可用节点\\n# interface-name: eth0 # 路由器下根据情况指定出站接口,如clash的ip地址***********,别用eth0,网卡会变化,电脑端不需要\\nglobal-client-fingerprint: chrome # 规避某些网站对代理工具的检测,使代理连接看起来像是从Chrome浏览器发出的\\nkeep-alive-idle: 600  # tcp保持连接,减少重连频率,提高连接稳定性\\nkeep-alive-interval: 15   # 设置发送保活探测包的间隔为15秒\\ndisable-keep-alive: false\\t# 为true时,表示禁用keep-alive-idle和keep-alive-interval\\nprofile:  # 策略组选择和fakeip缓存\\n    store-selected: true    # 选择节点后,重启内核能继续保持该节点的选择\\n    store-fake-ip: true     # 保存fake-ip模式下的IP映射缓存,减少DNS解析次数,提高访问速度。重启后继续使用之前的域名到IP的映射关系\\n    \\n# -----------------------------控制面板-----------------------------\\nexternal-controller: 0.0.0.0:9090   # 127.0.0.1则只允许本机访问控制面板,0.0.0.0的话,局域网所有设备都能访问.如果做了外网映射,可以用固定ip\\nsecret: \\\"Yyh7991\\\"    # 控制面板密码,公网暴露在外面的话则需要设置下\\nexternal-ui: \\\"/etc/mihomo/run/ui\\\"   # 存放web界面文件的下载路径\\nexternal-ui-name: zashboard\\t# 路径下面再建个文件夹,区分不同面板用的\\nexternal-ui-url: \\\"https://mirror.ghproxy.com/https://github.com/Zephyruso/zashboard/archive/refs/heads/gh-pages.zip\\\"    # Clash控制面板web界面文件的存放目录\\n\\n# -----------------------------嗅探-----------------------------\\nsniffer:\\n  enable: true\\n  sniff:\\n    HTTP:\\n      ports: [80, 8080-8880]\\n      override-destination: true\\n    TLS:\\n      ports: [443, 8443]\\n    QUIC:\\n      ports: [443, 8443]\\n  force-domain:\\t# 强制嗅探\\n    - \\\"+.v2ex.com\\\"\\n  skip-domain:\\t# 不想嗅探.中国大陆的域名不需要嗅探就都写上\\n    - \\\"rule-set:private_domain,cn_domain\\\"\\n    - \\\"dlg.io.mi.com\\\"\\n    - \\\"+.push.apple.com\\\"\\n    - \\\"+.apple.com\\\"\\n    - \\\"+.wechat.com\\\"\\n    - \\\"+.qpic.cn\\\"\\n    - \\\"+.qq.com\\\"\\n    - \\\"+.wechatapp.com\\\"\\n    - \\\"+.vivox.com\\\"\\n    - \\\"+.oray.com\\\"\\n    - \\\"+.sunlogin.net\\\"\\n    - \\\"+.msftconnecttest.com\\\"\\n    - \\\"+.msftncsi.com\\\"\\n\\n# -----------------------------流量入站-----------------------------\\ntun:\\n  enable: true    # 开启后所有流量都会通过Clash,无需在软件里单独设置代理\\n  stack: mixed    # system/gvisor/mixed, 出现兼容性问题就选system,正常用mixed或gvisor\\n  dns-hijack: [\\\"any:53\\\", \\\"tcp://any:53\\\"] # 接管所有DNS请求,防止DNS泄露\\n  auto-route: true # ROS有自己的路由系统,让Clash接管路由可能导致冲突\\n  auto-redirect: true\\n  auto-detect-interface: true # 是否自动识别网卡\\n\\n# -----------------------------DNS设置-----------------------------\\ndns:\\n  enable: true\\n  listen: 0.0.0.0:53\\n  ipv6: false\\n  respect-rules: true\\n  enhanced-mode: fake-ip\\n  fake-ip-range: ********/8\\n  fake-ip-filter-mode: blacklist\\n  fake-ip-filter:\\n      - \\\"rule-set:private_domain,cn_domain\\\"\\t# private_domain私有ip域名,中国大陆域名\\n      - \\\"*.lan\\\"\\n      - \\\"*.localdomain\\\"\\n      - \\\"*.example\\\"\\n      - \\\"*.invalid\\\"\\n      - \\\"*.localhost\\\"\\n      - \\\"*.test\\\"\\n      - \\\"*.local\\\"\\n      - \\\"*.home.arpa\\\"\\n      - \\\"time.*.com\\\"\\n      - \\\"time.*.gov\\\"\\n      - \\\"time.*.edu.cn\\\"\\n      - \\\"time.*.apple.com\\\"\\n      - \\\"time1.*.com\\\"\\n      - \\\"time2.*.com\\\"\\n      - \\\"time3.*.com\\\"\\n      - \\\"time4.*.com\\\"\\n      - \\\"time5.*.com\\\"\\n      - \\\"time6.*.com\\\"\\n      - \\\"time7.*.com\\\"\\n      - \\\"ntp.*.com\\\"\\n      - \\\"ntp1.*.com\\\"\\n      - \\\"ntp2.*.com\\\"\\n      - \\\"ntp3.*.com\\\"\\n      - \\\"ntp4.*.com\\\"\\n      - \\\"ntp5.*.com\\\"\\n      - \\\"ntp6.*.com\\\"\\n      - \\\"ntp7.*.com\\\"\\n      - \\\"*.time.edu.cn\\\"\\n      - \\\"*.ntp.org.cn\\\"\\n      - \\\"+.apple.com\\\"\\n      - \\\"+.pool.ntp.org\\\"\\n      - \\\"+.services.googleapis.cn\\\"\\t# 谷歌商店很多下载不下来的,可以加上\\n      - \\\"+.xn--ngstr-1ra8j.com\\\"\\n      - \\\"+.ntp.tencent.com\\\"\\n      - \\\"+.ntp1.aliyun.com\\\"\\n      - \\\"+.ntp.ntsc.ac.cn\\\"\\n      - \\\"+.cn.ntp.org.cn\\\"\\n      - \\\"time1.cloud.tencent.com\\\"\\n      - \\\"music.163.com\\\"\\n      - \\\"*.music.163.com\\\"\\n      - \\\"*.126.net\\\"\\n      - \\\"musicapi.taihe.com\\\"\\n      - \\\"music.taihe.com\\\"\\n      - \\\"songsearch.kugou.com\\\"\\n      - \\\"trackercdn.kugou.com\\\"\\n      - \\\"*.kuwo.cn\\\"\\n      - \\\"api-jooxtt.sanook.com\\\"\\n      - \\\"api.joox.com\\\"\\n      - \\\"joox.com\\\"\\n      - \\\"y.qq.com\\\"\\n      - \\\"*.y.qq.com\\\"\\n      - \\\"streamoc.music.tc.qq.com\\\"\\n      - \\\"mobileoc.music.tc.qq.com\\\"\\n      - \\\"isure.stream.qqmusic.qq.com\\\"\\n      - \\\"dl.stream.qqmusic.qq.com\\\"\\n      - \\\"aqqmusic.tc.qq.com\\\"\\n      - \\\"amobile.music.tc.qq.com\\\"\\n      - \\\"*.xiami.com\\\"\\n      - \\\"*.music.migu.cn\\\"\\n      - \\\"music.migu.cn\\\"\\n      - \\\"*.msftconnecttest.com\\\"\\n      - \\\"*.msftncsi.com\\\"\\n      - \\\"msftconnecttest.com\\\"\\n      - \\\"msftncsi.com\\\"\\n      - \\\"localhost.ptlogin2.qq.com\\\"\\n      - \\\"localhost.sec.qq.com\\\"\\n      - \\\"+.srv.nintendo.net\\\"\\n      - \\\"+.stun.playstation.net\\\"\\n      - \\\"*.microsoft.com\\\"\\n      - \\\"xbox.*.microsoft.com\\\"\\n      - \\\"xnotify.xboxlive.com\\\"\\n      - \\\"+.battlenet.com.cn\\\"\\n      - \\\"+.wotgame.cn\\\"\\n      - \\\"+.wggames.cn\\\"\\n      - \\\"+.wowsgame.cn\\\"\\n      - \\\"+.wargaming.net\\\"\\n      - \\\"proxy.golang.org\\\"\\n      - \\\"stun.*.*\\\"\\n      - \\\"stun.*.*.*\\\"\\n      - \\\"+.stun.*.*\\\"\\n      - \\\"+.stun.*.*.*\\\"\\n      - \\\"+.stun.*.*.*.*\\\"\\n      - \\\"*.mcdn.bilivideo.cn\\\"\\n  default-nameserver:\\n    *dns_default\\n  proxy-server-nameserver:\\n    *dns_cn\\n  # namesever尽量用运营商提供的DNS\\n  nameserver:\\n    *dns_default\\n\\n  # direct-nameserver: # 所有定义了直连的规则,比如访问百度走的是直连,那百度就用这个dns服务器去解析\\n  #   *dns_cn\\n  # direct-nameserver-follow-policy: false # 默认false,如果改为true,会提升nameserver-policy优先级,那direct-nameserver作用就不太大了(优先级低于nameserver-policy,DNS的规则)\\n  # nameserver-policy: # dns的匹配规则,告诉mihomo,访问什么就用什么dns服务器\\n  #   \\\"+.baidu.com\\\": \\\"114.114.114.114\\\" # \\\"域名\\\":\\\"dns服务器\\\"\\n  #   \\\"rule-set:private_domain,cn_domain\\\": # 这个规则集里的域名则用下面的服务器\\n  #     *dns_cn\\n  #   \\\"rule-set:gfw_domain,geolocation-!cn\\\": # 这个规则集里的域名则用下面的服务器\\n  #     - tls://8.8.4.4# 默认代理 # 加了#后,表示dns服务器连接的时候走代理\\n  #     - tls://1.1.1.1# 默认代理&h3=true # 不仅走代理还打开http3的模式\\n  # # 国外DNS服务器\\n  # fallback: # 可删除, 请求国外域名的部分就用这个,是老clash内核中为了防止dns污染所做的一个参数.但通常分流规则已经做的非常好,外加有DNS规则(nameserver-policy)加持,则fallback和fallback-filter这些参数就没必要了,反而复杂了dns的解析流程\\n  #   *dns_abroad\\n      \\n  # # 作用：当 Clash 使用默认的 nameserver 进行 DNS 查询后，根据查询结果（主要是 IP 地址）来判断这个结果是否\\\"可信\\\"\\n  # # 查询结果匹配了 fallback-filter 中的任何规则（geoip, ipcidr, geosite, domain）：Clash 认为 nameserver 返回的结果是可信的（或者是不应使用 fallback 处理的情况）\\n  # fallback-filter: # 可删除\\n  #     geoip: true # 只要是大陆的部分就认为没dns污染,采用nameserver\\n  #     geoip-code: CN # 只要是大陆的部分就认为没dns污染,采用nameserver\\n  #     geosite: # 匹配到这个就认为有dns污染, 采用fallback\\n  #       - gfw\\n  #     ipcidr: # 匹配到这个就认为有dns污染, 采用fallback\\n  #       # 以下是内网IP段，不会使用国外DNS\\n  #       - 0.0.0.0/8\\n  #       - 10.0.0.0/8\\n  #       - **********/10\\n  #       - *********/8\\n  #       - ***********/16\\n  #       - **********/12\\n  #       - *********/24\\n  #       - *********/24\\n  #       - ***********/24\\n  #       - ***********/16\\n  #       - **********/15\\n  #       - ************/24\\n  #       - ***********/24\\n  #       - *********/4\\n  #       - 240.0.0.0/4\\n  #       - ***************/32\\n  #       - ::/128\\n  #       - ::1/128\\n  #       - ::ffff:0:0/96\\n  #       - 64:ff9b::/96\\n  #       - 100::/64\\n  #       - 2001::/32\\n  #       - 2001:20::/28\\n  #       - 2001:db8::/32\\n  #       - 2002::/16\\n  #       - fc00::/7\\n  #       - fe80::/10\\n  #       - ff00::/8\\n  #     # 这些网站强制使用国外DNS解析，避免被污染\\n  #     domain: # 匹配到这个就认为有dns污染, 采用fallback\\n  #       - \\\"+.google.com\\\"\\n  #       - \\\"+.facebook.com\\\"\\n  #       - \\\"+.youtube.com\\\"\\n  #       - \\\"+.githubusercontent.com\\\"\\n  #       - \\\"+.googlevideo.com\\\"\\n# -----------------------------出站策略-----------------------------\\n# 先定义下面这部分,把节点筛选出来,然后定义规则匹配\\nproxy-groups:\\n    # 按媒体分组\\n    # 按地区分组\\n    - {name: 全部手动, <<: *S1, icon: *All}\\n    - {name: 默认代理, type: load-balance, proxies: [香港自动, 日本自动, 美国自动], interval: 300, strategy: round-robin,icon: *Default} # 可以filter: \\\"^((?!(直连)).)*$\\\"  filter掉送中的节点\\n    - {name: 香港自动, <<: *LB, filter: *HK, icon: \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Hong_Kong.png\\\"}\\n    - {name: 日本自动, <<: *URL, filter: *JP, icon: \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Japan.png\\\"}\\n    - {name: 韩国自动, <<: *URL, filter: *KR, icon: \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/South_Korea.png\\\"}\\n    - {name: 美国自动, <<: *URL, filter: *US, icon: \\\"https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/USA.png\\\"}\\n    # - {name: 链式代理, dialer-proxy: 默认代理, 落地节点信息..(type,server,port...)} # 落地节点经过谁中转(这边是默认代理)\\n\\n# -----------------------------规则集-----------------------------\\nrule-providers:\\t# 主参数\\n  # 规则集名字: { <<: 参数, url:\\\"规则集下载链接\\\"}\\n  private_domain: { <<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/private.mrs\\\"}\\n  # proxylite: {<<: *class, url: \\\"https://raw.githubusercontent.com/qichiyuhub/rule/refs/heads/master/ProxyLite.list\\\"} # 示例:list规则集的添加\\n  ai: { <<: *domain, url: \\\"https://github.com/MetaCubex/meta-rules-dat/raw/refs/heads/meta/geo/geosite/category-ai-chat-!cn.mrs\\\"}\\n  youtube_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/youtube.mrs\\\"}\\n  google_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/google.mrs\\\"}\\n  github_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/github.mrs\\\"}\\n  telegram_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/telegram.mrs\\\"}\\n  netflix_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/netflix.mrs\\\"}\\n  paypal_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/paypal.mrs\\\"}\\n  onedrive_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/onedrive.mrs\\\"}\\n  microsoft_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/microsoft.mrs\\\"}\\n  apple_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/apple-cn.mrs\\\"}\\n  speedtest_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs\\\"}\\n  tiktok_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/tiktok.mrs\\\"}\\n  gfw_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/gfw.mrs\\\"}\\n  geolocation-!cn: {<<: *domain, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs\\\"}\\n  cn_domain: {<<: *domain, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/cn.mrs\\\"}\\n\\n  private_ip: { <<: *ip, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geoip/private.mrs\\\"}\\n  cn_ip: {<<: *ip, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geoip/cn.mrs\\\"}\\n  google_ip: {<<: *ip, url: \\\"https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geoip/google.mrs\\\"}\\n  telegram_ip: {<<: *ip, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geoip/telegram.mrs\\\"}\\n  netflix_ip: {<<: *ip, url: \\\"https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geoip/netflix.mrs\\\"}\\n  \\n# -----------------------------规则匹配-----------------------------\\nrules:\\n  # - DOMAIN-SUFFIX, qichiyu.com, 默认代理\\t# 自定义某个网站域名\\n  # - SRC-IP-CIDR, *************/32, 直连\\t# 自定义某台设备的IP,走直连\\n  - RULE-SET, private_domain, DIRECT\\n  - RULE-SET, private_ip, DIRECT\\n  - RULE-SET, apple_domain, DIRECT\\n  - RULE-SET, ai, 美国自动\\n  - RULE-SET, github_domain, 默认代理\\n  - RULE-SET, youtube_domain, 默认代理 \\n  - RULE-SET, google_domain, 默认代理\\n  - RULE-SET, onedrive_domain, DIRECT\\n  - RULE-SET, microsoft_domain, DIRECT\\n  - RULE-SET, tiktok_domain, 默认代理\\n  - RULE-SET, speedtest_domain, 默认代理\\n  - RULE-SET, telegram_domain, 默认代理\\n  - RULE-SET, netflix_domain, 默认代理\\n  - RULE-SET, paypal_domain, 默认代理\\n  - RULE-SET, gfw_domain, 默认代理\\n  - RULE-SET, geolocation-!cn, 默认代理\\n  - RULE-SET, cn_domain, DIRECT\\n  - RULE-SET, google_ip, 默认代理, no-resolve\\t# 碰到ip规则,不加no-resolve就会开始DNS解析了\\n  - RULE-SET, netflix_ip, 默认代理, no-resolve\\n  - RULE-SET, telegram_ip, 默认代理, no-resolve\\n  - RULE-SET, cn_ip, DIRECT\\t# 大陆ip会进行解析匹配,要是这个再不匹配,下面就直接走代理了,那就会造成很多中国网站全部走了国外解析/国外代理,从外国绕了一圈再回来,不值得.\\n  - MATCH, 默认代理\\t# 兜底规则,走代理就行\",\n      \"display-name\": \"\",\n      \"url\": \"\",\n      \"subInfoUserAgent\": \"ClashMeta\",\n      \"proxy\": \"\",\n      \"mergeSources\": \"\",\n      \"isIconColor\": true,\n      \"ignoreFailedRemoteFile\": false\n    },\n    {\n      \"name\": \"Surge\",\n      \"displayName\": \"\",\n      \"remark\": \"\",\n      \"icon\": \"\",\n      \"source\": \"local\",\n      \"sourceType\": \"collection\",\n      \"sourceName\": \"\",\n      \"process\": [],\n      \"type\": \"file\",\n      \"content\": \"// 填入文件内容\\n\",\n      \"display-name\": \"\"\n    },\n    {\n      \"name\": \"clash\",\n      \"displayName\": \"\",\n      \"remark\": \"\",\n      \"icon\": \"\",\n      \"source\": \"local\",\n      \"sourceType\": \"collection\",\n      \"sourceName\": \"\",\n      \"process\": [],\n      \"type\": \"file\",\n      \"content\": \"#（一区）=============================（clash配置）\\nport: 7890\\nallow-lan: true\\nmode: rule\\nlog-level: info\\nunified-delay: true\\nglobal-client-fingerprint: chrome\\ndns:\\n  enable: true\\n  listen: :53\\n  ipv6: true\\n  enhanced-mode: fake-ip\\n  fake-ip-range: **********/16\\n  default-nameserver: \\n    - *********\\n    - *******\\n  nameserver:\\n    - https://dns.alidns.com/dns-query\\n    - https://doh.pub/dns-query\\n  fallback:\\n    - https://*******/dns-query\\n    - tls://dns.google\\n  fallback-filter:\\n    geoip: true\\n    geoip-code: CN\\n    ipcidr:\\n      - 240.0.0.0/4\\n\\n\\n#（二区）==============================（代理协议）\\nproxies:\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"香港 01\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":10001,\\\"uuid\\\":\\\"6fd8cd23-e95b-4f48-b84b-ec3511ddeed6\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"xcCOhoZcbjtMf-S9AQi-ZPB_Oni8LG_lmQkfIDbagSk\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"香港 02\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":10002,\\\"password\\\":\\\"6fd8cd23-e95b-4f48-b84b-ec3511ddeed6\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"香港 03\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":10001,\\\"uuid\\\":\\\"a386a1f3-7301-4a35-88da-b57101d6badb\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"EOngRDBf51f8-A0vEZgAXPpx3Vym18o4lrr2GGHexkg\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"香港 04\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":10002,\\\"password\\\":\\\"a386a1f3-7301-4a35-88da-b57101d6badb\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"香港 05\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8881,\\\"uuid\\\":\\\"6251a905-89ef-46ea-b72c-dbac2aede4ee\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"0_X0_AhIelZpJ6Iq11j1Hd3eUi3eEJTng9PhUaWx_w8\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"香港 06\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8882,\\\"password\\\":\\\"6251a905-89ef-46ea-b72c-dbac2aede4ee\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"香港 07\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8881,\\\"uuid\\\":\\\"1464ee16-6bfe-41ef-b95d-1461f10ad99c\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"4kr2tW1F9gh18HbHU8BiViSMotDErmvXzLdzg6wKy3s\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"香港 08\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8882,\\\"password\\\":\\\"1464ee16-6bfe-41ef-b95d-1461f10ad99c\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"香港 09\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8881,\\\"uuid\\\":\\\"2c6f135d-4abf-4dd2-a90b-e0ebe6d0ae31\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"jBgP3-qyOCMPMti9kCixcRO4YmEAItitil72urjF_X8\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"香港 10\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8882,\\\"password\\\":\\\"2c6f135d-4abf-4dd2-a90b-e0ebe6d0ae31\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"香港 11\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8881,\\\"uuid\\\":\\\"5944497f-e170-4c25-b752-2b1c7e13f5f8\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"bbo0l1PkOLejcdU2mNqUDWv9rV7Bl3SNHsIHRnLuahw\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"香港 12\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":8882,\\\"password\\\":\\\"5944497f-e170-4c25-b752-2b1c7e13f5f8\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"日本 01\\\",\\\"server\\\":\\\"************\\\",\\\"port\\\":10001,\\\"uuid\\\":\\\"cbcf3895-71e0-4cb6-aa3c-efce487c2a23\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"Y3D7TWF6y4zC5XSoFb2DPgjjIsRhKvRkDxKvEVhTE2M\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"日本 02\\\",\\\"server\\\":\\\"************\\\",\\\"port\\\":10002,\\\"password\\\":\\\"cbcf3895-71e0-4cb6-aa3c-efce487c2a23\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"韩国 01\\\",\\\"server\\\":\\\"************\\\",\\\"port\\\":8881,\\\"uuid\\\":\\\"b13ae3d3-7c27-4872-8cdc-74bb5dec944a\\\",\\\"tls\\\":true,\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"dM3c2HH-t7ur7LZ5OwoCoGQD1iQBCwxslt9lHPfkhgo\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"韩国 02\\\",\\\"server\\\":\\\"************\\\",\\\"port\\\":8882,\\\"password\\\":\\\"b13ae3d3-7c27-4872-8cdc-74bb5dec944a\\\",\\\"skip-cert-verify\\\":true,\\\"udp\\\":\\\"1\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"美国 01\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":10000,\\\"uuid\\\":\\\"9893af12-8e36-414c-a2af-febc42fb24c6\\\",\\\"tls\\\":true,\\\"client-fingerprint\\\":\\\"chrome\\\",\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"f1dfyPjrxsuf3GRbPPxjDAB1skKCj1jJRE5hGL12olY\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"美国 02\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":6000,\\\"uuid\\\":\\\"985058b5-545a-46e1-b59f-059f14eb529c\\\",\\\"tls\\\":true,\\\"client-fingerprint\\\":\\\"chrome\\\",\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"XIRAWTtCIAeujLj3WjzaM8bDUxe3uUDaIhOcGHX4bEs\\\"},\\\"network\\\":\\\"tcp\\\",\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"vless\\\",\\\"name\\\":\\\"美国 03\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":6001,\\\"uuid\\\":\\\"985058b5-545a-46e1-b59f-059f14eb529c\\\",\\\"tls\\\":true,\\\"client-fingerprint\\\":\\\"chrome\\\",\\\"skip-cert-verify\\\":false,\\\"reality-opts\\\":{\\\"public-key\\\":\\\"XIRAWTtCIAeujLj3WjzaM8bDUxe3uUDaIhOcGHX4bEs\\\"},\\\"network\\\":\\\"grpc\\\",\\\"grpc-opts\\\":{\\\"grpc-service-name\\\":\\\"grpc\\\"},\\\"servername\\\":\\\"addons.mozilla.org\\\"}\\n  - {\\\"type\\\":\\\"anytls\\\",\\\"name\\\":\\\"美国 04\\\",\\\"server\\\":\\\"*************\\\",\\\"port\\\":6002,\\\"password\\\":\\\"985058b5-545a-46e1-b59f-059f14eb529c\\\",\\\"skip-cert-verify\\\":true}\\n\\n#（三区）==============================（代理分流）\\n#分流组可自行创建添加，proxies参数下的name节点名称，按需求自行增减，确保出现的name节点名称在二区代理协议中可查找\\nproxy-groups:\\n- name: 负载均衡\\n  type: load-balance\\n  url: https://cp.cloudflare.com/generate_204\\n  interval: 300\\n  strategy: round-robin\\n  proxies:\\n    - 香港 01\\n    - 香港 02\\n    - 香港 03\\n    - 香港 04\\n    - 香港 05\\n    - 香港 07\\n    - 香港 08\\n    - 香港 09\\n    - 香港 10\\n    - 日本 01\\n    - 日本 02\\n    - 韩国 01\\n    - 韩国 02\\n    - 美国 01\\n    - 美国 02\\n    - 美国 03\\n    - 美国 04\\n\\n- name: 自动选择\\n  type: url-test\\n  url: https://cp.cloudflare.com/generate_204\\n  interval: 300\\n  tolerance: 50\\n  proxies:\\n    - vless-reality-vision节点                            #自定义添加协议的name字段\\n    - vless-reality-grpc节点\\n    \\n- name: 默认\\n  type: select\\n  proxies:\\n    - 负载均衡                                            #自定义添加协议的name字段\\n    - 自动选择\\n    - DIRECT\\n    - 香港 01\\n    - 香港 02\\n    - 香港 03\\n    - 香港 04\\n    - 香港 05\\n    - 香港 07\\n    - 香港 08\\n    - 香港 09\\n    - 香港 10\\n    - 日本 01\\n    - 日本 02\\n    - 韩国 01\\n    - 韩国 02\\n    - 美国 01\\n    - 美国 02\\n    - 美国 03\\n    - 美国 04\\n\\n#（四区）==============================（代理规则）\\nrule-providers:\\n  private_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.list\\\"\\n  cn_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.list\\\"\\n  github_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.list\\\"\\n  twitter_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/twitter.list\\\"\\n  youtube_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.list\\\"\\n  google_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.list\\\"\\n  telegram_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.list\\\"\\n  openai_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/openai.list\\\"    \\n  microsoft_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.list\\\"      \\n  geolocation-!cn:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.list\\\"\\n  gfw_domain:\\n    type: http\\n    interval: 86400\\n    behavior: domain\\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.list\\\"         \\n\\n  private_ip:\\n    type: http\\n    interval: 86400\\n    behavior: ipcidr \\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/private.list\\\"\\n  cn_ip:\\n    type: http\\n    interval: 86400\\n    behavior: ipcidr \\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.list\\\"\\n  google_ip:\\n    type: http\\n    interval: 86400\\n    behavior: ipcidr \\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.list\\\"\\n  netflix_ip:\\n    type: http\\n    interval: 86400\\n    behavior: ipcidr \\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.list\\\"\\n  twitter_ip:\\n    type: http\\n    interval: 86400\\n    behavior: ipcidr \\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/twitter.list\\\"\\n  telegram_ip:\\n    type: http\\n    interval: 86400\\n    behavior: ipcidr \\n    format: text\\n    url: \\\"https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.list\\\"\\n\\n\\nrules:\\n  # - GEOIP,LAN,DIRECT\\n  # - GEOIP,CN,DIRECT\\n  # - MATCH,默认\\n  - RULE-SET,private_ip,DIRECT,no-resolve\\n  - RULE-SET,github_domain,默认\\n  - RULE-SET,twitter_domain,默认\\n  - RULE-SET,youtube_domain,默认\\n  - RULE-SET,google_domain,默认\\n  - RULE-SET,telegram_domain,默认\\n  - RULE-SET,cn_domain,DIRECT\\n  - RULE-SET,openai_domain,默认\\n  - RULE-SET,microsoft_domain,默认\\n  - RULE-SET,geolocation-!cn,默认\\n  - RULE-SET,gfw_domain,默认  \\n\\n  - RULE-SET,google_ip,默认\\n  - RULE-SET,telegram_ip,默认\\n  - RULE-SET,twitter_ip,默认\\n  - RULE-SET,cn_ip,DIRECT\\n  - MATCH,默认\\n\\n  - GEOIP,lan,DIRECT,no-resolve\\n  - GEOSITE,github,默认\\n  - GEOSITE,twitter,默认\\n  - GEOSITE,youtube,默认\\n  - GEOSITE,google,默认\\n  - GEOSITE,telegram,默认\\n  - GEOSITE,CN,DIRECT\\n  - GEOSITE,geolocation-!cn,默认\\n\\n  - GEOIP,google,默认\\n  - GEOIP,telegram,默认\\n  - GEOIP,twitter,默认\\n  - GEOIP,CN,DIRECT\",\n      \"display-name\": \"\"\n    }\n  ],\n  \"tokens\": [],\n  \"schemaVersion\": \"2.0\",\n  \"settings\": {\n    \"syncPlatform\": \"\",\n    \"githubUser\": \"yyh357\",\n    \"gistToken\": \"****************************************\",\n    \"defaultUserAgent\": \"\",\n    \"defaultProxy\": \"\",\n    \"defaultTimeout\": \"\",\n    \"cacheThreshold\": \"\",\n    \"avatarUrl\": \"https://avatars.githubusercontent.com/u/130017463?v=4\",\n    \"artifactStoreStatus\": \"NOT FOUND\",\n    \"appearanceSetting\": {\n      \"isSimpleMode\": false,\n      \"isLeftRight\": \"\",\n      \"isDefaultIcon\": \"\",\n      \"isIconColor\": true,\n      \"isShowIcon\": true,\n      \"isSimpleShowRemark\": \"\",\n      \"isEditorCommon\": true,\n      \"isSimpleReicon\": \"\",\n      \"isSubItemMenuFold\": true,\n      \"showFloatingRefreshButton\": true,\n      \"showFloatingAddButton\": true,\n      \"displayPreviewInWebPage\": true,\n      \"istabBar\": false,\n      \"istabBar2\": false,\n      \"subProgressStyle\": \"hidden\"\n    },\n    \"syncTime\": 1753024976720\n  },\n  \"modules\": []\n}"}