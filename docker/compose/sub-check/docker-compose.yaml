services:
  subs-check:
    image: ghcr.io/beck-8/subs-check:latest
    container_name: subs-check
    networks: 
      - 1panel-network
    volumes:
      - ./config:/app/config
      - ./output:/app/output
    ports:
      - "8299:8299"
      - "8199:8199"
    environment:
      - TZ=Asia/Shanghai
      # - HTTP_PROXY=http://192.168.1.1:7890
      # - HTTPS_PROXY=http://192.168.1.1:7890
      # - API_KEY=password
    restart: always

networks:
    1panel-network:
        external: true



