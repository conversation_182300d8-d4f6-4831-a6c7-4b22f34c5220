[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "http_proxy_pool"
version = "0.1.0"
description = "动态http代理池"
readme = "README.md"
authors = [
    {name = "yyh357", email = "<EMAIL>"},
]
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
dependencies = [
    "environs",
    "Flask",
    "attrs",
    "retrying",
    "aiohttp",
    "requests",
    "loguru",
    "pyquery",
    "redis",
    "lxml",
    "fake_headers"
]

[tool.setuptools.packages.find]
where = ["."]
include = ["http_proxy_pool*"]
exclude = ["tests*", "examples*", "logs*", "*.egg-info*", "__pycache__*", ".env*"]