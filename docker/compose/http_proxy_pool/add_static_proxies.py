#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从txt文件读取代理并添加到Redis脚本
"""

import os
from http_proxy_pool.storages.redis_client import RedisClient
from http_proxy_pool.schemas.proxy import Proxy

def load_proxies_from_file(file_path):
    """从txt文件读取代理列表"""
    if not os.path.exists(file_path):
        print(f"ERROR: 文件不存在: {file_path}")
        return []

    proxy_list = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # 跳过空行和注释
                    proxy_list.append(line)
                    print(f"读取第{line_num}行: {line}")

        print(f"SUCCESS: 从文件读取到 {len(proxy_list)} 个代理")
        return proxy_list

    except Exception as e:
        print(f"ERROR: 读取文件失败: {e}")
        return []

def add_proxies_to_redis(file_path="static_proxies.txt", redis_key="proxies:universal"):
    """从txt文件读取代理并添加到Redis指定房间"""

    print("=" * 60)
    print("静态代理添加到Redis脚本")
    print("=" * 60)

    # 读取代理文件
    print(f"1. 读取代理文件: {file_path}")
    proxy_list = load_proxies_from_file(file_path)

    if not proxy_list:
        print("ERROR: 没有读取到任何代理，退出")
        return False

    print(f"2. 开始添加代理到Redis...")
    print(f"   目标房间: {redis_key}")
    print(f"   代理数量: {len(proxy_list)}")
    print("=" * 60)

    # 创建Redis客户端
    try:
        redis_client = RedisClient()
        print("SUCCESS: Redis客户端连接成功")
    except Exception as e:
        print(f"ERROR: Redis连接失败: {e}")
        return False

    # 记录添加前的代理数量
    try:
        before_count = redis_client.count(redis_key=redis_key)
        print(f"添加前{redis_key}房间代理数: {before_count}")
    except Exception as e:
        print(f"WARNING: 无法获取添加前的代理数量: {e}")
        before_count = 0

    # 添加代理
    success_count = 0
    failed_proxies = []

    for i, proxy_url in enumerate(proxy_list, 1):
        try:
            # 正确解析代理URL，提取host和port
            # 格式: *****************************:port
            if '://' in proxy_url:
                # 去掉协议前缀
                url_without_protocol = proxy_url.split('://', 1)[1]

                if '@' in url_without_protocol:
                    # 有认证信息
                    auth_part, host_port = url_without_protocol.split('@', 1)
                    if ':' in host_port:
                        host, port_str = host_port.rsplit(':', 1)
                        port = int(port_str)
                        # 重新构建完整的host（包含协议和认证）
                        protocol = proxy_url.split('://', 1)[0]
                        full_host = f"{protocol}://{auth_part}@{host}"
                    else:
                        # 没有端口，使用默认端口
                        full_host = proxy_url
                        port = 80
                else:
                    # 没有认证信息
                    if ':' in url_without_protocol:
                        host, port_str = url_without_protocol.rsplit(':', 1)
                        port = int(port_str)
                        protocol = proxy_url.split('://', 1)[0]
                        full_host = f"{protocol}://{host}"
                    else:
                        full_host = proxy_url
                        port = 80
            else:
                # 没有协议前缀，直接解析
                if ':' in proxy_url:
                    full_host, port_str = proxy_url.rsplit(':', 1)
                    port = int(port_str)
                else:
                    full_host = proxy_url
                    port = 80

            # 创建Proxy对象
            proxy = Proxy(host=full_host, port=port)

            # 添加到Redis指定房间
            redis_client.add(proxy, redis_key=redis_key)

            print(f"SUCCESS {i:2d}. {proxy_url} - 添加成功")
            success_count += 1

        except Exception as e:
            print(f"ERROR {i:2d}. {proxy_url} - 添加失败: {e}")
            failed_proxies.append(proxy_url)

    print("=" * 60)
    print(f"3. 添加完成！成功: {success_count}/{len(proxy_list)}")

    if failed_proxies:
        print(f"失败的代理:")
        for proxy in failed_proxies:
            print(f"  - {proxy}")

    # 严格验证添加结果
    print("=" * 60)
    print("4. 严格验证Redis存储结果...")
    return verify_redis_storage(redis_client, redis_key, proxy_list, before_count, success_count)

def verify_redis_storage(redis_client, redis_key, original_proxies, before_count, expected_success):
    """严格验证Redis中的代理存储"""

    try:
        # 获取添加后的总数量
        after_count = redis_client.count(redis_key=redis_key)
        print(f"添加后{redis_key}房间代理数: {after_count}")

        # 验证数量增长
        actual_increase = after_count - before_count
        print(f"实际增加数量: {actual_increase}")
        print(f"期望增加数量: {expected_success}")

        if actual_increase == expected_success:
            print("SUCCESS: 代理数量验证通过")
        else:
            print(f"WARNING: 代理数量不匹配！期望增加{expected_success}，实际增加{actual_increase}")

        # 获取Redis中的所有代理进行详细验证
        print("=" * 60)
        print("5. 详细验证每个代理是否存在...")

        all_proxies = redis_client.all(redis_key=redis_key)
        redis_proxy_strings = [proxy.string() for proxy in all_proxies]

        print(f"Redis中总代理数: {len(redis_proxy_strings)}")

        # 验证每个原始代理是否存在
        found_count = 0
        not_found = []

        for proxy_url in original_proxies:
            # 检查完整URL格式
            if proxy_url in redis_proxy_strings:
                found_count += 1
                print(f"FOUND: {proxy_url}")
            # 检查URL:0格式（因为我们设置port=0）
            elif f"{proxy_url}:0" in redis_proxy_strings:
                found_count += 1
                print(f"FOUND: {proxy_url}:0")
            else:
                not_found.append(proxy_url)
                print(f"NOT FOUND: {proxy_url}")

        print("=" * 60)
        print("6. 最终验证结果:")
        print(f"原始代理数量: {len(original_proxies)}")
        print(f"成功添加数量: {expected_success}")
        print(f"Redis中找到数量: {found_count}")
        print(f"Redis总代理数: {len(redis_proxy_strings)}")

        if found_count == expected_success:
            print("SUCCESS: 所有代理都已正确存储到Redis！")
            return True
        else:
            print(f"ERROR: 验证失败！期望找到{expected_success}个，实际找到{found_count}个")
            if not_found:
                print("未找到的代理:")
                for proxy in not_found:
                    print(f"  - {proxy}")
            return False

    except Exception as e:
        print(f"ERROR: 验证过程失败: {e}")
        return False

if __name__ == '__main__':
    # 默认读取static_proxies.txt文件，添加到proxies:universal房间
    file_path = "static_proxies.txt"
    redis_key = "proxies:universal"

    print(f"开始执行静态代理添加任务...")
    print(f"文件路径: {file_path}")
    print(f"目标Redis房间: {redis_key}")

    success = add_proxies_to_redis(file_path, redis_key)

    if success:
        print("\n" + "=" * 60)
        print("任务完成！所有代理已成功添加并验证！")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("任务失败！请检查错误信息并重试！")
        print("=" * 60)
