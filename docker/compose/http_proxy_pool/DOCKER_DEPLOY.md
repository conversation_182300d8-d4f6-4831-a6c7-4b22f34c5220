# HTTP Proxy Pool 部署与API使用指南

## 🚀 快速部署

### 1. 部署服务
```bash
# 启动服务
docker-compose up -d

# 检查状态
docker ps
```

### 2. 验证部署
```bash
# 测试API连接
curl -H "API-KEY: yqproxy" http://localhost:5555/count
```

## 🔧 配置说明

### 修改配置
编辑 `.env` 文件，然后重启容器：
```bash
# 修改配置
vim .env

# 重启应用配置
docker-compose restart
```

### 主要配置项
| 配置项 | 说明 | 当前值 |
|:---|:---|:---|
| `API_KEY` | API访问密钥 | yqproxy |
| `CYCLE_GETTER` | 获取周期（秒） | 300 |
| `CYCLE_TESTER` | 测试周期（秒） | 60 |

## 🌐 API使用指南

### API端点列表
| 端点 | 功能 | 返回 |
|:---|:---|:---|
| `GET /` | 首页 | 欢迎信息 |
| `GET /random` | 获取随机代理 | 代理地址 |
| `GET /count` | 获取代理数量 | 数字 |
| `GET /all` | 获取所有代理 | 代理列表 |

### 基础用法
```bash
# 获取随机代理
curl -H "API-KEY: yqproxy" http://localhost:5555/random

# 获取代理数量
curl -H "API-KEY: yqproxy" http://localhost:5555/count

# 获取所有代理
curl -H "API-KEY: yqproxy" http://localhost:5555/all
```

### Python使用示例
```python
import requests

# 配置
API_KEY = "yqproxy"
BASE_URL = "http://localhost:5555"
headers = {"API-KEY": API_KEY}

# 获取随机代理
response = requests.get(f"{BASE_URL}/random", headers=headers)
proxy = response.text.strip()
print(f"获取到代理: {proxy}")

# 使用代理访问网站
proxies = {"http": f"http://{proxy}", "https": f"http://{proxy}"}
data = requests.get("https://example.com", proxies=proxies)
```

## 🔍 服务管理

### 查看状态
```bash
# 查看容器状态
docker ps

# 查看日志
docker logs http_proxy_pool

# 实时日志
docker logs -f http_proxy_pool
```

### 重启服务
```bash
# 重启容器
docker-compose restart

# 停止服务
docker-compose down

# 启动服务
docker-compose up -d
```

## 🛠️ 常见问题

### 1. API返回400错误
```bash
# 检查是否提供了API密钥
curl -H "API-KEY: yqproxy" http://localhost:5555/count
```

### 2. 代理数量为0
```bash
# 查看getter日志，检查代理获取情况
docker logs http_proxy_pool | grep getter
```

### 3. 修改配置
```bash
# 编辑.env文件
vim .env

# 重启应用配置
docker-compose restart
```
