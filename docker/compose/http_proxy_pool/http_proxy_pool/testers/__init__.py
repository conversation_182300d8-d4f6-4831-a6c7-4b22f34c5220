import pkgutil
import importlib
from .base import BaseTester
import inspect


# load classes subclass of BaseTester using modern importlib
classes = []
for loader, name, is_pkg in pkgutil.walk_packages(__path__):
    try:
        # 使用现代的importlib方式
        module_name = f'{__name__}.{name}'
        module = importlib.import_module(module_name)
        for attr_name, value in inspect.getmembers(module):
            globals()[attr_name] = value
            if inspect.isclass(value) and issubclass(value, BaseTester) and value is not BaseTester \
                    and not getattr(value, 'ignore', False):
                classes.append(value)
    except Exception as e:
        # 忽略导入错误，继续处理其他模块
        pass
__all__ = __ALL__ = classes

