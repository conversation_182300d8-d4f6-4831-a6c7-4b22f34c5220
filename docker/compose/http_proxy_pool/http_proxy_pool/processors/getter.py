from loguru import logger
from http_proxy_pool.storages.redis_client import RedisClient
from http_proxy_pool.setting import PROXY_NUMBER_MAX_TOTAL, PROXY_NUMBER_MIN_INVALID, PROXY_SCORE_MAX, REDIS_KEY
from http_proxy_pool.crawlers import __all__ as crawlers_cls
from http_proxy_pool.testers import __all__ as testers_cls

class Getter(object):
    """
    getter of proxypool
    """

    def __init__(self):
        """
        init db and crawlers
        """
        self.redis = RedisClient()
        self.crawlers_cls = crawlers_cls
        self.crawlers = [crawler_cls() for crawler_cls in self.crawlers_cls]
        self.testers_cls = testers_cls
        self.testers = [tester_cls() for tester_cls in self.testers_cls]

    def is_full(self):
        """
        if proxypool if full
        return: bool
        """
        return self.redis.count() >= PROXY_NUMBER_MAX_TOTAL

    def get_valid_proxy_count(self):
        """
        get count of valid proxies (score = PROXY_SCORE_MAX)
        return: int
        """
        return len(self.redis.db.zrangebyscore(REDIS_KEY, PROXY_SCORE_MAX, PROXY_SCORE_MAX))

    def is_domain_proxy(self, proxy_string):
        """
        check if proxy is domain-based (static proxy)
        return: bool
        """
        # 检查是否包含域名（而不是纯IP）
        if '://' in proxy_string:
            # 提取host部分
            url_part = proxy_string.split('://', 1)[1]
            if '@' in url_part:
                host_port = url_part.split('@', 1)[1]
            else:
                host_port = url_part

            if ':' in host_port:
                host = host_port.rsplit(':', 1)[0]
            else:
                host = host_port

            # 检查是否为域名（包含字母）
            return any(c.isalpha() for c in host)
        return False

    def cleanup_invalid_proxies(self):
        """
        智能清理无效代理
        删除分数在[11,70]范围内的动态代理（非域名代理）
        """
        logger.info("开始智能清理无效代理...")

        # 获取分数在[11,70]范围内的代理
        invalid_proxies = self.redis.db.zrangebyscore(REDIS_KEY, 11, 70, withscores=True)

        if not invalid_proxies:
            logger.info("没有找到需要清理的代理")
            return 0

        deleted_count = 0
        for proxy_string, score in invalid_proxies:
            proxy_string = proxy_string.decode() if isinstance(proxy_string, bytes) else proxy_string

            # 只删除动态代理（非域名代理）
            if not self.is_domain_proxy(proxy_string):
                try:
                    self.redis.db.zrem(REDIS_KEY, proxy_string)
                    deleted_count += 1
                    logger.debug(f"删除无效动态代理: {proxy_string} (分数: {score})")
                except Exception as e:
                    logger.error(f"删除代理失败: {proxy_string} - {e}")
            else:
                logger.debug(f"保留静态代理: {proxy_string} (分数: {score})")

        logger.info(f"智能清理完成，删除了 {deleted_count} 个无效动态代理")
        return deleted_count

    def should_cleanup(self):
        """
        判断是否需要进行智能清理
        条件：总代理数达到上限 且 有效代理数不足最小要求
        """
        total_count = self.redis.count()
        valid_count = self.get_valid_proxy_count()

        should_clean = (total_count >= PROXY_NUMBER_MAX_TOTAL and
                       valid_count < PROXY_NUMBER_MIN_INVALID)

        if should_clean:
            logger.info(f"触发智能清理条件: 总代理数={total_count}/{PROXY_NUMBER_MAX_TOTAL}, "
                       f"有效代理数={valid_count}/{PROXY_NUMBER_MIN_INVALID}")

        return should_clean

    @logger.catch
    def run(self):
        """
        run crawlers to get proxy
        智能代理池管理：
        1. 检查是否需要清理无效代理
        2. 如果代理池满了但有效代理不足，进行智能清理
        3. 继续获取新代理
        :return:
        """
        # 智能清理检查
        if self.should_cleanup():
            deleted_count = self.cleanup_invalid_proxies()
            logger.info(f"智能清理完成，释放了 {deleted_count} 个代理位置")

        # 检查代理池是否仍然满了
        if self.is_full():
            logger.debug("代理池已满，跳过本次获取")
            return

        # 获取新代理
        for crawler in self.crawlers:
            logger.info(f'crawler {crawler} to get proxy')
            for proxy in crawler.crawl():
                self.redis.add(proxy)
                [self.redis.add(proxy, redis_key=tester.key) for tester in self.testers]


if __name__ == '__main__':
    getter = Getter()
    getter.run()
