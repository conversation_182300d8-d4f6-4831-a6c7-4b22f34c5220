# encoding: utf-8
import urllib.request
import urllib.parse
from loguru import logger
from http_proxy_pool.utils.proxy import convert_proxy_or_proxies
from http_proxy_pool.crawlers.base import BaseCrawler

API_SERVER_ADDRESS = 'http://uu-proxy.com/api/'
TOKEN_ID = 'ZD48ZCL4JC'
SIZE = 50
SCHEMES = 'http'
SUPPORT_HTTPS = 'true'
RESTIME_WITHIN_MS = 5000
FORMAT = 'txt2_2'


def get_expiretime(API_SERVER_ADDRESS, TOKEN_ID):
    query = {
        'id': TOKEN_ID
    }
    url = API_SERVER_ADDRESS + 'get_expiretime?' + urllib.parse.urlencode(query)
    res_data = urllib.request.urlopen(url)
    return res_data.read().decode('utf-8')

def get_proxies(API_SERVER_ADDRESS, TOKEN_ID, SIZ<PERSON>, SCHEM<PERSON>, SUPPORT_HTTPS, RESTIME_WITHIN_MS, FORMAT):
    query = {
        'id': TOKEN_ID,
        'size': SIZE,
        'schemes': SCHEMES,
        'support_https': SUPPORT_HTTPS,
        'restime_within_ms': RESTIME_WITHIN_MS,
        'format': FORMAT
    }
    url = API_SERVER_ADDRESS + 'get_proxies?' + urllib.parse.urlencode(query)
    res_data = urllib.request.urlopen(url)
    return res_data.read().decode('utf-8')


class UUProxyCrawler(BaseCrawler):
    """
    UU代理爬虫 - 继承BaseCrawler
    直接调用上面的get_proxies函数，转换为Proxy对象
    """

    def crawl(self):
        """
        爬虫主方法，yield Proxy对象
        """
        try:
            logger.info('开始获取UU代理...')

            # 调用上面的get_proxies函数
            proxy_text = get_proxies(
                API_SERVER_ADDRESS,
                TOKEN_ID,
                SIZE,
                SCHEMES,
                SUPPORT_HTTPS,
                RESTIME_WITHIN_MS,
                FORMAT
            )

            if not proxy_text:
                logger.warning('UU代理API返回空数据')
                return

            # 处理每一行代理
            proxy_lines = proxy_text.strip().split('\n')
            logger.info(f'获取到 {len(proxy_lines)} 个代理')

            for line in proxy_lines:
                line = line.strip()
                if not line:
                    continue

                try:
                    # 保存原始完整URL
                    original_line = line

                    # 转换为Proxy对象（使用完整URL）
                    proxy = convert_proxy_or_proxies(original_line)
                    if proxy:
                        logger.info(f'解析代理: {proxy.string()}')
                        yield proxy
                    else:
                        logger.warning(f'无效代理格式: {line}')

                except Exception as e:
                    logger.error(f'解析代理失败 {line}: {e}')
                    continue

        except Exception as e:
            logger.error(f'UU代理爬虫运行失败: {e}')


if __name__ == '__main__':
    # 原有功能保持不变
    # res = get_expiretime(API_SERVER_ADDRESS, TOKEN_ID)
    # print(u'过期时间：')
    # print(res)

    res = get_proxies(
        API_SERVER_ADDRESS,
        TOKEN_ID,
        SIZE,
        SCHEMES,
        SUPPORT_HTTPS,
        RESTIME_WITHIN_MS,
        FORMAT
    )
    print(res)

    # 新增：测试爬虫功能
    print("\n" + "="*50)
    print("测试爬虫功能：")
    crawler = UUProxyCrawler()
    count = 0
    for proxy in crawler.crawl():
        print(f'代理: {proxy.string()}')
        count += 1
    print(f'总共获取到 {count} 个代理')
