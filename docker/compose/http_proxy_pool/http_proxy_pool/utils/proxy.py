from http_proxy_pool.schemas import Proxy


def is_valid_proxy(data):
    """
    check this string is within proxy format
    支持格式：
    - IP:端口 (如：***********:8080)
    - username:password@IP:端口 (如：user:pass@***********:8080)
    - http://IP:端口 (自动去除协议前缀)
    - ***************************:端口 (自动去除协议前缀)
    """
    if '://' in data:
        data = data.split('://', 1)[1]

    if is_auth_proxy(data):
        host, port = extract_auth_proxy(data)
        return is_ip_valid(host) and is_port_valid(port)
    elif data.__contains__(':'):
        ip = data.split(':')[0]
        port = data.split(':')[1]
        return is_ip_valid(ip) and is_port_valid(port)
    else:
        return is_ip_valid(data)


def is_ip_valid(ip):
    """
    check this string is within ip format or domain name format
    支持IP地址和域名
    """
    if is_auth_proxy(ip):
        ip = ip.split('@')[1]

    # 检查是否为IP地址
    a = ip.split('.')
    if len(a) == 4:
        # 尝试作为IP地址验证
        try:
            for x in a:
                if not x.isdigit():
                    # 不是纯数字，可能是域名，进行域名验证
                    return is_domain_valid(ip)
                i = int(x)
                if i < 0 or i > 255:
                    return False
            return True  # 是有效的IP地址
        except:
            return is_domain_valid(ip)
    else:
        # 不是4段格式，可能是域名
        return is_domain_valid(ip)

def is_domain_valid(domain):
    """
    check this string is within domain name format
    简单的域名格式验证
    """
    if not domain or len(domain) > 253:
        return False

    # 域名不能以点开始或结束
    if domain.startswith('.') or domain.endswith('.'):
        return False

    # 分割域名各部分
    parts = domain.split('.')
    if len(parts) < 2:  # 至少要有两部分，如 example.com
        return False

    for part in parts:
        if not part:  # 空部分
            return False
        if len(part) > 63:  # 每部分不能超过63字符
            return False
        # 检查字符是否合法（字母、数字、连字符）
        if not all(c.isalnum() or c == '-' for c in part):
            return False
        # 不能以连字符开始或结束
        if part.startswith('-') or part.endswith('-'):
            return False

    return True


def is_port_valid(port):
    return port.isdigit()


def convert_proxy_or_proxies(data):
    """
    convert list of str to valid proxies or proxy
    支持带协议前缀的代理，会保留完整URL作为host
    :param data:
    :return:
    """
    if not data:
        return None
    # if list of proxies
    if isinstance(data, list):
        result = []
        for item in data:
            # skip invalid item
            item = item.strip()
            if not is_valid_proxy(item):
                continue

            # 保存原始完整URL
            original_item = item

            # 去除协议前缀进行解析
            if '://' in item:
                item = item.split('://', 1)[1]

            if is_auth_proxy(item):
                host, port = extract_auth_proxy(item)
                # 保留完整的原始URL作为host（包含协议和认证信息）
                host = original_item.rsplit(':', 1)[0]  # 去掉最后的端口号
            else:
                host, port, *_ = item.split(':')
                # 如果原始有协议前缀，保留它
                if '://' in original_item:
                    protocol = original_item.split('://', 1)[0]
                    host = f"{protocol}://{host}"

            result.append(Proxy(host=host, port=int(port)))
        return result

    if isinstance(data, str) and is_valid_proxy(data):
        # 保存原始完整URL
        original_data = data

        # 去除协议前缀进行解析
        if '://' in data:
            data = data.split('://', 1)[1]

        if is_auth_proxy(data):
            host, port = extract_auth_proxy(data)
            # 保留完整的原始URL作为host（包含协议和认证信息）
            host = original_data.rsplit(':', 1)[0]  # 去掉最后的端口号
        else:
            host, port = data.split(':')
            # 如果原始有协议前缀，保留它
            if '://' in original_data:
                protocol = original_data.split('://', 1)[0]
                host = f"{protocol}://{host}"

        return Proxy(host=host, port=int(port))


def is_auth_proxy(data: str) -> bool:
    return '@' in data


def extract_auth_proxy(data: str) -> (str, str):
    """
    extract host and port from a proxy with authentication
    """
    auth = data.split('@')[0]
    ip_port = data.split('@')[1]
    ip = ip_port.split(':')[0]
    port = ip_port.split(':')[1]
    host = auth + '@' + ip
    return host, port


if __name__ == '__main__':
    proxy = 'http://proxy:<EMAIL>:51080'
    print(extract_auth_proxy(proxy))
