# HTTP Proxy Pool 环境变量配置说明

## 📋 支持的环境变量列表

### 🔧 Redis数据库配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `PROXYPOOL_REDIS_HOST` | string | 127.0.0.1 | Redis主机地址 | ✅ |
| `PROXYPOOL_REDIS_PORT` | int | 6379 | Redis端口 | ✅ |
| `PROXYPOOL_REDIS_PASSWORD` | string | None | Redis密码 | ✅ |
| `PROXYPOOL_REDIS_DB` | int | 0 | Redis数据库编号 | ✅ |
| `PROXYPOOL_REDIS_KEY` | string | proxies:universal | 代理池键名 | ✅ |
| `PROXYPOOL_REDIS_CONNECTION_STRING` | string | None | Redis连接字符串 | ❌ |
| `REDIS_HOST` | string | 127.0.0.1 | 备用Redis主机 | ❌ |
| `REDIS_PORT` | int | 6379 | 备用Redis端口 | ❌ |
| `REDIS_PASSWORD` | string | None | 备用Redis密码 | ❌ |
| `REDIS_DB` | int | 0 | 备用Redis数据库 | ❌ |
| `REDIS_KEY` | string | proxies:universal | 备用代理池键名 | ❌ |
| `REDIS_CONNECTION_STRING` | string | None | 备用Redis连接字符串 | ❌ |

### 🌐 API服务配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `API_HOST` | string | 0.0.0.0 | API服务监听地址 | ✅ |
| `API_PORT` | int | 5555 | API服务端口 | ✅ |
| `API_KEY` | string | '' | API访问密钥 | ✅ |
| `API_THREADED` | bool | true | API多线程支持 | ❌ |

### ⚙️ 核心服务开关

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `ENABLE_GETTER` | bool | true | 启用代理获取服务 | ✅ |
| `ENABLE_TESTER` | bool | true | 启用代理测试服务 | ✅ |
| `ENABLE_SERVER` | bool | true | 启用API服务 | ✅ |

### ⏰ 运行周期配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `CYCLE_GETTER` | int | 100 | 代理获取周期（秒） | ✅ |
| `CYCLE_TESTER` | int | 20 | 代理测试周期（秒） | ✅ |
| `GET_TIMEOUT` | int | 10 | 获取超时时间（秒） | ❌ |

### 🧪 代理测试配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `TEST_URL` | string | 东方财富API | 测试URL | ✅ |
| `TEST_TIMEOUT` | int | 10 | 测试超时时间（秒） | ❌ |
| `TEST_BATCH` | int | 20 | 测试批次大小 | ❌ |
| `TEST_ANONYMOUS` | bool | false | 是否测试匿名性 | ✅ |
| `TEST_VALID_STATUS` | list | [200,206,302] | 有效状态码 | ❌ |
| `TEST_DONT_SET_MAX_SCORE` | bool | false | 不设置最高分 | ❌ |

### 📊 代理分数配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `PROXY_SCORE_MAX` | int | 100 | 代理最高分数 | ✅ |
| `PROXY_SCORE_MIN` | int | 0 | 代理最低分数 | ✅ |
| `PROXY_SCORE_INIT` | int | 10 | 代理初始分数 | ✅ |

### 📈 代理数量限制配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `PROXY_NUMBER_MAX` | int | 50000 | 代理池最大容量 | ❌ |
| `PROXY_NUMBER_MIN` | int | 0 | 代理池最小容量 | ❌ |

### 📝 日志配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `LOG_DIR` | string | logs | 日志目录 | ❌ |
| `LOG_ROTATION` | string | 500MB | 日志轮转大小 | ✅ |
| `LOG_RETENTION` | string | 1 week | 日志保留时间 | ✅ |
| `LOG_RUNTIME_FILE` | string | logs/runtime.log | 运行日志文件 | ❌ |
| `LOG_ERROR_FILE` | string | logs/error.log | 错误日志文件 | ❌ |
| `ENABLE_LOG_FILE` | bool | true | 启用文件日志 | ❌ |
| `ENABLE_LOG_RUNTIME_FILE` | bool | true | 启用运行日志 | ❌ |
| `ENABLE_LOG_ERROR_FILE` | bool | true | 启用错误日志 | ❌ |

### 🚀 应用环境配置

| 环境变量 | 类型 | 默认值 | 说明 | 当前使用 |
|:---|:---|:---|:---|:---|
| `APP_ENV` | string | dev | 应用环境(dev/test/prod) | ❌ |
| `APP_DEBUG` | bool | true | 调试模式 | ❌ |
| `APP_PROD_METHOD` | string | gevent | 生产环境方法 | ❌ |

## ✅ 所有配置项现已支持环境变量

所有setting.py中的配置项现在都支持通过环境变量进行配置！

## 📝 使用建议

### ✅ 推荐在.env中配置的项目：
- Redis连接配置（必需）
- API配置（必需）
- 核心服务开关
- 运行周期
- 代理分数配置

### ❌ 可以注释掉的项目：
- 备用Redis配置（如果使用PROXYPOOL_前缀）
- 详细的测试配置（使用默认值）
- 日志详细配置（使用默认值）
- 应用环境配置（Docker环境下不需要）

## 🔧 配置优先级

1. **PROXYPOOL_前缀** > **无前缀**（Redis配置）
2. **环境变量** > **默认值**
3. **连接字符串** > **单独配置**（Redis）
