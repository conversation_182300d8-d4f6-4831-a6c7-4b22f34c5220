# 多阶段构建，优化镜像大小
FROM python:3.11-alpine AS build

# 复制依赖文件
COPY pyproject.toml .

# 安装构建依赖和Python包
RUN apk update && \
    apk add --no-cache gcc g++ libffi-dev openssl-dev libxml2-dev libxslt-dev build-base musl-dev && \
    pip install -U pip && \
    pip install --timeout 30 --user --no-cache-dir --no-warn-script-location -e .

# 生产镜像
FROM python:3.11-alpine

# 设置环境变量
ENV APP_ENV=prod
ENV LOCAL_PKG="/root/.local"
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 从构建阶段复制安装的包
COPY --from=build ${LOCAL_PKG} ${LOCAL_PKG}

# 安装运行时依赖
RUN apk update && \
    apk add --no-cache libffi-dev openssl-dev libxslt-dev supervisor && \
    ln -sf ${LOCAL_PKG}/bin/* /usr/local/bin/

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露API端口
EXPOSE 5555

# 创建数据卷（用于自定义爬虫）
VOLUME ["/app/http_proxy_pool/crawlers/private"]

# 使用supervisor管理多进程
ENTRYPOINT ["supervisord", "-c", "supervisord.conf"]
