2025/07/21 12:13:41 [error] 84#84: *28742 recv() failed (104: Connection reset by peer) while reading response header from upstream, client: ************, server: subapi.proxygo.de, request: "GET /suWjf72Afbnvd8234UbS/api/utils/env HTTP/2.0", upstream: "http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/api/utils/env", host: "subapi.proxygo.de", referrer: "https://sub-store.vercel.app/"
2025/07/21 12:13:42 [error] 84#84: *28742 recv() failed (104: Connection reset by peer) while reading response header from upstream, client: ************, server: subapi.proxygo.de, request: "GET /suWjf72Afbnvd8234UbS/api/utils/env HTTP/2.0", upstream: "http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/api/utils/env", host: "subapi.proxygo.de", referrer: "https://sub-store.vercel.app/"
2025/07/21 12:13:52 [error] 84#84: *28742 recv() failed (104: Connection reset by peer) while reading response header from upstream, client: ************, server: subapi.proxygo.de, request: "GET /suWjf72Afbnvd8234UbS/api/utils/env HTTP/2.0", upstream: "http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/api/utils/env", host: "subapi.proxygo.de", referrer: "https://sub-store.vercel.app/"
2025/07/21 12:13:55 [error] 84#84: *28742 recv() failed (104: Connection reset by peer) while reading response header from upstream, client: ************, server: subapi.proxygo.de, request: "GET /suWjf72Afbnvd8234UbS/api/utils/env HTTP/2.0", upstream: "http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/api/utils/env", host: "subapi.proxygo.de", referrer: "https://sub-store.vercel.app/"
2025/07/21 12:13:56 [error] 84#84: *28742 recv() failed (104: Connection reset by peer) while reading response header from upstream, client: ************, server: subapi.proxygo.de, request: "GET /suWjf72Afbnvd8234UbS/api/utils/env HTTP/2.0", upstream: "http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/api/utils/env", host: "subapi.proxygo.de", referrer: "https://sub-store.vercel.app/"
2025/07/21 12:13:57 [error] 84#84: *28742 recv() failed (104: Connection reset by peer) while reading response header from upstream, client: ************, server: subapi.proxygo.de, request: "GET /suWjf72Afbnvd8234UbS/api/utils/env HTTP/2.0", upstream: "http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/api/utils/env", host: "subapi.proxygo.de", referrer: "https://sub-store.vercel.app/"
2025/07/21 12:13:57 [error] 84#84: *28742 recv() failed (104: Connection reset by peer) while reading response header from upstream, client: ************, server: subapi.proxygo.de, request: "GET /suWjf72Afbnvd8234UbS/api/utils/env HTTP/2.0", upstream: "http://127.0.0.1:8299/suWjf72Afbnvd8234UbS/api/utils/env", host: "subapi.proxygo.de", referrer: "https://sub-store.vercel.app/"
