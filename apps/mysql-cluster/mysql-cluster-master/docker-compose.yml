networks:
    1panel-network:
        external: true
services:
    mysql-cluster-master:
        container_name: ${CONTAINER_NAME}
        deploy:
            resources:
                limits:
                    cpus: ${CPUS}
                    memory: ${MEMORY_LIMIT}
        environment:
            MYSQL_AUTHENTICATION_PLUGIN: mysql_native_password
            MYSQL_REPLICATION_MODE: master
            MYSQL_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD}
            MYSQL_REPLICATION_USER: ${REPLICATION_USER}
            MYSQL_ROOT_PASSWORD: ${PANEL_DB_ROOT_PASSWORD}
        healthcheck:
            interval: 15s
            retries: 6
            test:
                - CMD
                - /opt/bitnami/scripts/mysql/healthcheck.sh
            timeout: 5s
        image: bitnami/mysql:8.4
        labels:
            createdBy: Apps
        networks:
            - 1panel-network
        ports:
            - ${HOST_IP}:${PANEL_APP_PORT_HTTP}:3306
        restart: always
        volumes:
            - ./data/:/bitnami/mysql/data
            - ./conf/my.cnf:/opt/bitnami/mysql/conf/my.cnf
            - /etc/localtime:/etc/localtime:ro
