networks:
    1panel-network:
        external: true
services:
    watchtower:
        command: ${COMMAND1}
        container_name: ${CONTAINER_NAME}
        deploy:
            resources:
                limits:
                    cpus: ${CPUS}
                    memory: ${MEMORY_LIMIT}
        environment:
            - TZ=Asia/Shanghai
            - ${ENV1}
        image: containrrr/watchtower
        labels:
            createdBy: Apps
        networks:
            - 1panel-network
        restart: always
        volumes:
            - /var/run/docker.sock:/var/run/docker.sock
