additionalProperties:
    formFields:
        - default: WATCHTOWER_NO_STARTUP_MESSAGE
          edit: true
          envKey: ENV1
          labelEn: Environmental parameters (split by ;)
          labelZh: 环境参数(以;分割)
          label:
            en: Environmental parameters (split by ;)
            ja: 環境パラメータ(分割)
            ms: Parameter persekitaran (pisahkan dengan ;)
            pt-br: Parâmetros ambientais (divididos por ;)
            ru: Параметры среды (разделенные ;)
            ko: 환경 매개변수(구분 기호로 나누기)
            zh: 环境参数(以;分割)
            zh-Hant: 環境參數(以;分割)
          required: true
          type: text
        - default: --interval 3600 --cleanup
          edit: true
          envKey: COMMAND1
          labelEn: Command arguments (space-separated, single quotes not supported)
          labelZh: 命令参数（以空格分隔，不支持单引号）
          label:
            en: Command arguments (space-separated, single quotes not supported)
            ja: コマンド引数（スペースで区切り、シングルクォートはサポートされていません）
            ms: Argumen perintah (dipisahkan dengan ruang, tanda petik tunggal tidak disokong)
            pt-br: Argumentos de comando (separados por espaço, aspas simples não suportadas)
            ru: Аргументы команды (разделенные пробелами, одинарные кавычки не поддерживаются)
            ko: 명령 인수(공백으로 구분, 작은따옴표는 지원하지 않음)
            zh: 命令参数（以空格分隔，不支持单引号）
            zh-Hant: 命令參數（以空格分隔，不支持單引號）
          required: true
          type: text
