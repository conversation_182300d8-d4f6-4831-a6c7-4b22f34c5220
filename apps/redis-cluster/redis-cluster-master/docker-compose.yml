networks:
    1panel-network:
        external: true
services:
    redis-cluster-master:
        container_name: ${CONTAINER_NAME}
        deploy:
            resources:
                limits:
                    cpus: ${CPUS}
                    memory: ${MEMORY_LIMIT}
        environment:
            REDIS_PASSWORD: ${PANEL_REDIS_ROOT_PASSWORD}
            REDIS_REPLICATION_MODE: master
        image: bitnami/redis:8.0
        labels:
            createdBy: Apps
        networks:
            - 1panel-network
        ports:
            - ${HOST_IP}:${PANEL_APP_PORT_HTTP}:6379
        restart: always
        volumes:
            - ./data:/bitnami/redis/data
            - ./conf:/opt/bitnami/redis/etc.default
            - ./logs:/opt/bitnami/redis/logs
