networks:
    1panel-network:
        external: true
services:
    postgresql-cluster-master:
        container_name: ${CONTAINER_NAME}
        deploy:
            resources:
                limits:
                    cpus: ${CPUS}
                    memory: ${MEMORY_LIMIT}
        environment:
            - POSTGRESQL_REPLICATION_MODE=master
            - POSTGRESQL_REPLICATION_USER=${REPLICATION_USER}
            - POSTGRESQL_REPLICATION_PASSWORD=${REPLICATION_PASSWORD}
            - POSTGRESQL_PASSWORD=${PANEL_DB_ROOT_PASSWORD}
        healthcheck:
            interval: 30s
            retries: 5
            start_period: 20s
            test:
                - CMD
                - pg_isready
                - -h
                - 127.0.0.1
                - -p
                - "5432"
                - -q
                - -U
                - postgres
            timeout: 5s
        image: bitnami/postgresql:17
        labels:
            createdBy: Apps
        networks:
            - 1panel-network
        ports:
            - ${HOST_IP}:${PANEL_APP_PORT_HTTP}:5432
        restart: always
        volumes:
            - ./postgresql:/bitnami/postgresql
            - ./data:/bitnami/postgresql/conf
