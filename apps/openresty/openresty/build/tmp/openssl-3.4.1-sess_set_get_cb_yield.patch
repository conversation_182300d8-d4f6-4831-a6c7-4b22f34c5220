diff --git a/include/openssl/bio.h.in b/include/openssl/bio.h.in
index 440d838..8ff2c5d 100644
--- a/include/openssl/bio.h.in
+++ b/include/openssl/bio.h.in
@@ -285,6 +285,8 @@ void BIO_clear_flags(BIO *b, int flags);
 /* Returned from the accept BIO when an accept would have blocked */
 # define BIO_RR_ACCEPT                   0x03
 
+# define BIO_RR_SSL_SESSION_LOOKUP 0x09
+
 /* These are passed by the BIO callback */
 # define BIO_CB_FREE        0x01
 # define BIO_CB_READ        0x02
diff --git a/include/openssl/ssl.h.in b/include/openssl/ssl.h.in
index 4bab2ac..b70b4fd 100644
--- a/include/openssl/ssl.h.in
+++ b/include/openssl/ssl.h.in
@@ -909,6 +909,7 @@ __owur int SSL_extension_supported(unsigned int ext_type);
 # define SSL_ASYNC_NO_JOBS      6
 # define SSL_CLIENT_HELLO_CB    7
 # define SSL_RETRY_VERIFY       8
+# define SSL_SESS_LOOKUP 99
 
 /* These will only be used when doing non-blocking IO */
 # define SSL_want_nothing(s)         (SSL_want(s) == SSL_NOTHING)
@@ -919,6 +920,7 @@ __owur int SSL_extension_supported(unsigned int ext_type);
 # define SSL_want_async(s)           (SSL_want(s) == SSL_ASYNC_PAUSED)
 # define SSL_want_async_job(s)       (SSL_want(s) == SSL_ASYNC_NO_JOBS)
 # define SSL_want_client_hello_cb(s) (SSL_want(s) == SSL_CLIENT_HELLO_CB)
+# define SSL_want_sess_lookup(s) (SSL_want(s) == SSL_SESS_LOOKUP)
 
 # define SSL_MAC_FLAG_READ_MAC_STREAM 1
 # define SSL_MAC_FLAG_WRITE_MAC_STREAM 2
@@ -1215,6 +1217,8 @@ DECLARE_PEM_rw(SSL_SESSION, SSL_SESSION)
 # define SSL_ERROR_WANT_ASYNC_JOB       10
 # define SSL_ERROR_WANT_CLIENT_HELLO_CB 11
 # define SSL_ERROR_WANT_RETRY_VERIFY    12
+# define SSL_ERROR_WANT_SESSION_LOOKUP 99
+# define SSL_ERROR_PENDING_SESSION      99 /* BoringSSL compatibility */
 
 # ifndef OPENSSL_NO_DEPRECATED_3_0
 #  define SSL_CTRL_SET_TMP_DH                    3
@@ -1740,6 +1744,7 @@ int SSL_SESSION_print(BIO *fp, const SSL_SESSION *ses);
 int SSL_SESSION_print_keylog(BIO *bp, const SSL_SESSION *x);
 int SSL_SESSION_up_ref(SSL_SESSION *ses);
 void SSL_SESSION_free(SSL_SESSION *ses);
+SSL_SESSION *SSL_magic_pending_session_ptr(void);
 __owur int i2d_SSL_SESSION(const SSL_SESSION *in, unsigned char **pp);
 __owur int SSL_set_session(SSL *to, SSL_SESSION *session);
 int SSL_CTX_add_session(SSL_CTX *ctx, SSL_SESSION *session);
diff --git a/ssl/bio_ssl.c b/ssl/bio_ssl.c
index ac65a39..3a4ffba 100644
--- a/ssl/bio_ssl.c
+++ b/ssl/bio_ssl.c
@@ -140,6 +140,10 @@ static int ssl_read(BIO *b, char *buf, size_t size, size_t *readbytes)
         BIO_set_retry_special(b);
         retry_reason = BIO_RR_SSL_X509_LOOKUP;
         break;
+    case SSL_ERROR_WANT_SESSION_LOOKUP:
+        BIO_set_retry_special(b);
+        retry_reason = BIO_RR_SSL_SESSION_LOOKUP;
+        break;
     case SSL_ERROR_WANT_ACCEPT:
         BIO_set_retry_special(b);
         retry_reason = BIO_RR_ACCEPT;
@@ -208,6 +212,10 @@ static int ssl_write(BIO *b, const char *buf, size_t size, size_t *written)
         BIO_set_retry_special(b);
         retry_reason = BIO_RR_SSL_X509_LOOKUP;
         break;
+    case SSL_ERROR_WANT_SESSION_LOOKUP:
+        BIO_set_retry_special(b);
+        retry_reason = BIO_RR_SSL_SESSION_LOOKUP;
+        break;
     case SSL_ERROR_WANT_CONNECT:
         BIO_set_retry_special(b);
         retry_reason = BIO_RR_CONNECT;
@@ -368,6 +376,10 @@ static long ssl_ctrl(BIO *b, int cmd, long num, void *ptr)
             BIO_set_retry_special(b);
             BIO_set_retry_reason(b, BIO_RR_SSL_X509_LOOKUP);
             break;
+        case SSL_ERROR_WANT_SESSION_LOOKUP:
+            BIO_set_retry_special(b);
+            BIO_set_retry_reason(b, BIO_RR_SSL_SESSION_LOOKUP);
+            break;
         default:
             break;
         }
diff --git a/ssl/ssl_lib.c b/ssl/ssl_lib.c
index 295b719..fa25f77 100644
--- a/ssl/ssl_lib.c
+++ b/ssl/ssl_lib.c
@@ -4713,6 +4713,8 @@ int ossl_ssl_get_error(const SSL *s, int i, int check_err)
         return SSL_ERROR_WANT_ASYNC_JOB;
     if (SSL_want_client_hello_cb(s))
         return SSL_ERROR_WANT_CLIENT_HELLO_CB;
+    if (SSL_want_sess_lookup(s))
+        return SSL_ERROR_WANT_SESSION_LOOKUP;
 
     if ((sc->shutdown & SSL_RECEIVED_SHUTDOWN) &&
         (sc->s3.warn_alert == SSL_AD_CLOSE_NOTIFY))
diff --git a/ssl/ssl_sess.c b/ssl/ssl_sess.c
index 69149de..9c1d48f 100644
--- a/ssl/ssl_sess.c
+++ b/ssl/ssl_sess.c
@@ -20,6 +20,8 @@
 #include "ssl_local.h"
 #include "statem/statem_local.h"
 
+static const char g_pending_session_magic = 0;
+
 static void SSL_SESSION_list_remove(SSL_CTX *ctx, SSL_SESSION *s);
 static void SSL_SESSION_list_add(SSL_CTX *ctx, SSL_SESSION *s);
 static int remove_session_lock(SSL_CTX *ctx, SSL_SESSION *c, int lck);
@@ -525,6 +527,10 @@ SSL_SESSION *lookup_sess_in_cache(SSL_CONNECTION *s,
         ret = s->session_ctx->get_session_cb(SSL_CONNECTION_GET_USER_SSL(s),
                                              sess_id, sess_id_len, &copy);
 
+        if (ret == SSL_magic_pending_session_ptr()) {
+            return ret; /* Retry later */
+        }
+
         if (ret != NULL) {
             if (ret->not_resumable) {
                 /* If its not resumable then ignore this session */
@@ -619,6 +625,9 @@ int ssl_get_prev_session(SSL_CONNECTION *s, CLIENTHELLO_MSG *hello)
                 try_session_cache = 1;
                 ret = lookup_sess_in_cache(s, hello->session_id,
                                            hello->session_id_len);
+                if (ret == SSL_magic_pending_session_ptr()) {
+                    return -2; /* Retry later */
+                }
             }
             break;
         case SSL_TICKET_NO_DECRYPT:
@@ -1094,6 +1103,11 @@ EVP_PKEY *SSL_SESSION_get0_peer_rpk(SSL_SESSION *s)
     return s->peer_rpk;
 }
 
+SSL_SESSION *SSL_magic_pending_session_ptr(void)
+{
+    return (SSL_SESSION *) &g_pending_session_magic;
+}
+
 int SSL_SESSION_set1_id_context(SSL_SESSION *s, const unsigned char *sid_ctx,
                                 unsigned int sid_ctx_len)
 {
diff --git a/ssl/statem/statem_srvr.c b/ssl/statem/statem_srvr.c
index 9f586b5..e42c1e3 100644
--- a/ssl/statem/statem_srvr.c
+++ b/ssl/statem/statem_srvr.c
@@ -1695,6 +1695,7 @@ static int tls_early_post_process_client_hello(SSL_CONNECTION *s)
     SSL_CTX *sctx = SSL_CONNECTION_GET_CTX(s);
     SSL *ssl = SSL_CONNECTION_GET_SSL(s);
     SSL *ussl = SSL_CONNECTION_GET_USER_SSL(s);
+    PACKET saved_ciphers;
 
     /* Finished parsing the ClientHello, now we can start processing it */
     /* Give the ClientHello callback a crack at things */
@@ -1775,6 +1776,7 @@ static int tls_early_post_process_client_hello(SSL_CONNECTION *s)
     }
 
     s->hit = 0;
+    saved_ciphers = clienthello->ciphersuites;
 
     if (!ssl_cache_cipherlist(s, &clienthello->ciphersuites,
                               clienthello->isv2) ||
@@ -1874,6 +1876,10 @@ static int tls_early_post_process_client_hello(SSL_CONNECTION *s)
         } else if (i == -1) {
             /* SSLfatal() already called */
             goto err;
+        } else if (i == -2) {
+            clienthello->ciphersuites = saved_ciphers;
+            s->rwstate = SSL_SESS_LOOKUP;
+            goto retry;
         } else {
             /* i == 0 */
             if (!ssl_get_new_session(s, 1)) {
@@ -1881,6 +1887,7 @@ static int tls_early_post_process_client_hello(SSL_CONNECTION *s)
                 goto err;
             }
         }
+        s->rwstate = SSL_NOTHING;
     }
 
     if (SSL_CONNECTION_IS_TLS13(s)) {
@@ -2134,6 +2141,11 @@ static int tls_early_post_process_client_hello(SSL_CONNECTION *s)
     s->clienthello = NULL;
 
     return 0;
+
+retry:
+    sk_SSL_CIPHER_free(ciphers);
+    sk_SSL_CIPHER_free(scsvs);
+    return -1;
 }
 
 /*
diff --git a/util/libssl.num b/util/libssl.num
index f1daba1..9b36444 100644
--- a/util/libssl.num
+++ b/util/libssl.num
@@ -7,6 +7,7 @@ SSL_copy_session_id                     6	3_0_0	EXIST::FUNCTION:
 SSL_CTX_set_srp_password                7	3_0_0	EXIST::FUNCTION:DEPRECATEDIN_3_0,SRP
 SSL_shutdown                            8	3_0_0	EXIST::FUNCTION:
 SSL_CTX_set_msg_callback                9	3_0_0	EXIST::FUNCTION:
+SSL_magic_pending_session_ptr     	10      3_0_0   EXIST::FUNCTION:
 SSL_SESSION_get0_ticket                 11	3_0_0	EXIST::FUNCTION:
 SSL_get1_supported_ciphers              12	3_0_0	EXIST::FUNCTION:
 SSL_state_string_long                   13	3_0_0	EXIST::FUNCTION:
