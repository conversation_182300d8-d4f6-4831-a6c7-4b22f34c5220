server {
    listen 80 default_server; 
    listen [::]:80 default_server; 
    listen 443 ssl default_server; 
    listen [::]:443 ssl default_server; 
    listen 443 quic reuseport default_server; 
    listen [::]:443 quic reuseport default_server; 
    server_name _; 
    index 404.html; 
    root /usr/share/nginx/html; 
    include /usr/local/openresty/nginx/conf/ssl/root_ssl.conf; 
    http2 on; 
}