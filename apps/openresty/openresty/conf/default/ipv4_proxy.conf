# IPv4+IPv6 HTTP正向代理配置 - 负载均衡版
server {
    listen 0.0.0.0:18888;
    server_name ipv4_ipv6_proxy;
    
    # DNS解析器 (支持IPv4和IPv6)
    resolver ******* ******* 2001:4860:4860::8888 2001:4860:4860::8844 valid=300s;
    resolver_timeout 10s;
    
    # 日志配置 - 记录负载均衡信息
    access_log /var/log/nginx/proxy_access.log;
    error_log /var/log/nginx/proxy_error.log;
    
    # 初始化负载均衡状态
    init_by_lua_block {
        -- IP池配置
        local ipv4_list = {"***************"}
        local ipv6_list = {"2406:ef80:1:5715::1"}
        
        -- 全局负载均衡状态
        ngx.shared.lb_state:set("current_type", "ipv4")
        ngx.shared.lb_state:set("ipv4_index", 1)
        ngx.shared.lb_state:set("ipv6_index", 1)
        ngx.shared.lb_state:set("use_ipv6", true)
        
        -- 存储IP列表
        for i, ip in ipairs(ipv4_list) do
            ngx.shared.lb_state:set("ipv4_" .. i, ip)
        end
        ngx.shared.lb_state:set("ipv4_count", #ipv4_list)
        
        for i, ip in ipairs(ipv6_list) do
            ngx.shared.lb_state:set("ipv6_" .. i, ip)
        end
        ngx.shared.lb_state:set("ipv6_count", #ipv6_list)
        
        ngx.log(ngx.INFO, "负载均衡初始化完成 - IPv4: " .. #ipv4_list .. "个, IPv6: " .. #ipv6_list .. "个")
    }
    
    # 代理逻辑
    location / {
        access_by_lua_block {
            -- 获取代理认证头
            local proxy_auth = ngx.var.http_proxy_authorization
            if not proxy_auth then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                ngx.say("Proxy Authentication Required")
                return ngx.exit(407)
            end
            
            -- 解析认证
            local auth_type, auth_data = string.match(proxy_auth, "(%w+)%s+(.+)")
            if auth_type ~= "Basic" then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                return ngx.exit(407)
            end
            
            -- 验证凭据
            local decoded = ngx.decode_base64(auth_data)
            if not decoded then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                return ngx.exit(407)
            end
            
            local username, password = string.match(decoded, "([^:]+):(.+)")
            if username ~= "proxy" or password ~= "Yyh275822746Xyz" then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                return ngx.exit(407)
            end
            
            -- 验证目标主机
            if not ngx.var.http_host then
                ngx.status = 400
                return ngx.exit(400)
            end
            
            -- 负载均衡IP选择
            local function select_outbound_ip()
                local use_ipv6 = ngx.shared.lb_state:get("use_ipv6")
                local current_type = ngx.shared.lb_state:get("current_type")
                local ipv4_count = ngx.shared.lb_state:get("ipv4_count") or 0
                local ipv6_count = ngx.shared.lb_state:get("ipv6_count") or 0
                
                if use_ipv6 and ipv6_count > 0 then
                    -- IPv4和IPv6交替使用
                    if current_type == "ipv4" then
                        -- 选择IPv4
                        local ipv4_index = ngx.shared.lb_state:get("ipv4_index") or 1
                        local selected_ip = ngx.shared.lb_state:get("ipv4_" .. ipv4_index)
                        
                        -- 更新索引（轮询）
                        local next_index = (ipv4_index % ipv4_count) + 1
                        ngx.shared.lb_state:set("ipv4_index", next_index)
                        ngx.shared.lb_state:set("current_type", "ipv6")
                        
                        return selected_ip, "ipv4"
                    else
                        -- 选择IPv6（随机）
                        local random_index = math.random(1, ipv6_count)
                        local selected_ip = ngx.shared.lb_state:get("ipv6_" .. random_index)
                        ngx.shared.lb_state:set("current_type", "ipv4")
                        
                        return selected_ip, "ipv6"
                    end
                else
                    -- 仅使用IPv4
                    local ipv4_index = ngx.shared.lb_state:get("ipv4_index") or 1
                    local selected_ip = ngx.shared.lb_state:get("ipv4_" .. ipv4_index)
                    
                    -- 更新索引（轮询）
                    local next_index = (ipv4_index % ipv4_count) + 1
                    ngx.shared.lb_state:set("ipv4_index", next_index)
                    
                    return selected_ip, "ipv4"
                end
            end
            
            -- 选择出站IP
            local outbound_ip, ip_type = select_outbound_ip()
            if outbound_ip then
                ngx.var.outbound_ip = outbound_ip
                ngx.var.ip_type = ip_type
                ngx.log(ngx.INFO, "选择出站IP: " .. outbound_ip .. " (类型: " .. ip_type .. ")")
            else
                ngx.log(ngx.ERR, "无法选择出站IP")
                ngx.status = 500
                return ngx.exit(500)
            end
        }
        
        # 使用选择的IP进行代理转发
        proxy_bind $outbound_ip;
        proxy_pass http://$http_host$request_uri;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Proxy-Authorization "";
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # HTTP设置
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_buffering off;
        proxy_redirect off;
    }
    
    # 健康检查
    location = /health {
        return 200 "IPv4+IPv6 Proxy OK";
        add_header Content-Type text/plain;
    }
    
    # 负载均衡状态查询
    location = /lb-status {
        access_by_lua_block {
            local current_type = ngx.shared.lb_state:get("current_type")
            local ipv4_count = ngx.shared.lb_state:get("ipv4_count") or 0
            local ipv6_count = ngx.shared.lb_state:get("ipv6_count") or 0
            local use_ipv6 = ngx.shared.lb_state:get("use_ipv6")
            
            ngx.say("负载均衡状态:")
            ngx.say("当前类型: " .. (current_type or "unknown"))
            ngx.say("IPv4数量: " .. ipv4_count)
            ngx.say("IPv6数量: " .. ipv6_count)
            ngx.say("IPv6启用: " .. tostring(use_ipv6))
        }
    }
}


