services:
    openresty:
        build:
            args:
                - PANEL_OPENRESTY_VERSION=********-2-1-focal
                - RESTY_CONFIG_OPTIONS_MORE=${RESTY_CONFIG_OPTIONS_MORE}
                - RESTY_ADD_PACKAGE_BUILDDEPS=${RESTY_ADD_PACKAGE_BUILDDEPS}
                - CONTAINER_PACKAGE_URL=${CONTAINER_PACKAGE_URL}
            context: ./build
        container_name: ${CONTAINER_NAME}
        deploy:
            resources:
                limits:
                    cpus: ${CPUS}
                    memory: ${MEMORY_LIMIT}
        image: 1panel/openresty:********-2-1-focal
        labels:
            createdBy: Apps
        network_mode: host
        restart: always
        volumes:
            - ./conf/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf
            - ./conf/fastcgi_params:/usr/local/openresty/nginx/conf/fastcgi_params
            - ./conf/fastcgi-php.conf:/usr/local/openresty/nginx/conf/fastcgi-php.conf
            - ./conf/mime.types:/usr/local/openresty/nginx/conf/mime.types
            - ./conf/default:/usr/local/openresty/nginx/conf/default/
            - ./conf/ssl:/usr/local/openresty/nginx/conf/ssl/
            - ./log:/var/log/nginx
            - ./root:/usr/share/nginx/html
            - /etc/localtime:/etc/localtime
            - ./1pwaf/data:/usr/local/openresty/1pwaf/data
            - ${WEBSITE_DIR}:/www
            - ${WEBSITE_DIR}/conf.d:/usr/local/openresty/nginx/conf/conf.d/
