{"rules": [{"state": "on", "rule": "\\.(htaccess|mysql_history|bash_history|DS_Store|git|env|idea|user\\.ini)", "name": "000001", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"state": "on", "name": "000002", "rule": "(?:etc\\/\\W*passwd)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"state": "on", "name": "000004", "rule": "\\.{2,}[\\/\\\\]|%2e%2e[%2f%5c]", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"state": "on", "name": "000005", "rule": "WEB-INF/web.xml", "type": "appFilter"}, {"state": "on", "name": "000006", "rule": "boaform/admin/formLogin", "type": "appFilter"}, {"state": "on", "name": "000007", "rule": "wp-includes/wlwmanifest.xml", "type": "appFilter"}]}