{"waf": {"state": "off", "secret": "", "mode": "protection", "token": ""}, "cc": {"state": "off", "code": 0, "action": "deny", "type": "cc", "res": "", "ipBlock": "on", "ipBlockTime": 600, "threshold": 100, "duration": 10, "mode": "uri"}, "geoRestrict": {"state": "off", "rules": [], "action": "deny", "type": "geoRestrict", "res": "geo"}, "methodWhite": {"state": "on", "code": 444, "action": "deny", "type": "methodW<PERSON>e", "res": ""}, "defaultUaBlack": {"state": "on", "code": 403, "action": "deny", "type": "defaultUaBlack", "res": ""}, "defaultUrlBlack": {"state": "on", "code": 403, "action": "deny", "type": "defaultUrlBlack", "res": ""}, "fileExt": {"state": "off", "code": 403, "action": "deny", "type": "fileExtCheck", "res": ""}, "header": {"state": "on", "code": 403, "action": "deny", "type": "header", "res": ""}, "cookie": {"state": "on", "code": 403, "action": "deny", "type": "cookie", "res": ""}, "args": {"state": "on", "code": 403, "action": "deny", "type": "args", "res": ""}, "xss": {"state": "on", "code": 403, "action": "deny", "type": "xss", "res": ""}, "sql": {"state": "on", "code": 403, "action": "deny", "type": "sql", "res": ""}, "cdn": {"state": "off"}, "strict": {"state": "off"}, "app": {"state": "off", "action": "allow", "type": "app", "rule": ""}}