{"rules": [{"state": "on", "rule": "GET", "name": "GET", "type": "httpMethod"}, {"state": "on", "rule": "POST", "name": "POST", "type": "httpMethod"}, {"state": "on", "rule": "PUT", "name": "PUT", "type": "httpMethod"}, {"state": "on", "rule": "DELETE", "name": "DELETE", "type": "httpMethod"}, {"state": "on", "rule": "PATCH", "name": "PATCH", "type": "httpMethod"}, {"state": "on", "rule": "HEAD", "name": "HEAD", "type": "httpMethod"}, {"state": "on", "rule": "OPTIONS", "name": "OPTIONS", "type": "httpMethod"}, {"state": "on", "rule": "TRACE", "name": "TRACE", "type": "httpMethod"}, {"state": "on", "rule": "CONNECT", "name": "CONNECT", "type": "httpMethod"}, {"state": "on", "rule": "PROPFIND", "name": "PROPFIND", "type": "httpMethod"}, {"state": "on", "rule": "PROPPATCH", "name": "PROPPATCH", "type": "httpMethod"}, {"state": "on", "rule": "MKCOL", "name": "MKCOL", "type": "httpMethod"}, {"state": "on", "rule": "COPY", "name": "COPY", "type": "httpMethod"}, {"state": "on", "rule": "MOVE", "name": "MOVE", "type": "httpMethod"}, {"state": "on", "rule": "LOCK", "name": "LOCK", "type": "httpMethod"}, {"state": "on", "rule": "UNLOCK", "name": "UNLOCK", "type": "httpMethod"}, {"state": "on", "rule": "LINK", "name": "LINK", "type": "httpMethod"}, {"state": "on", "rule": "UNLINK", "name": "UNLINK", "type": "httpMethod"}, {"state": "on", "rule": "WRAPPED", "name": "WRAPPED", "type": "httpMethod"}, {"state": "on", "rule": "SRARCH", "name": "SRARCH", "type": "httpMethod"}]}