{"waf": {"state": "off", "secret": "", "mode": "protection", "token": ""}, "ipWhite": {"state": "on", "code": 0, "action": "allow", "type": "ipWhite", "res": ""}, "ipBlack": {"state": "on", "code": 403, "action": "deny", "type": "ipBlack", "res": "ip"}, "urlWhite": {"state": "on", "code": 0, "action": "allow", "type": "u<PERSON><PERSON><PERSON><PERSON>", "res": ""}, "urlBlack": {"state": "on", "code": 403, "action": "deny", "type": "urlBlack", "res": ""}, "uaWhite": {"state": "off", "code": 0, "action": "allow", "type": "u<PERSON><PERSON><PERSON><PERSON>", "res": ""}, "uaBlack": {"state": "on", "code": 403, "action": "deny", "type": "uaBlack", "res": ""}, "notFoundCount": {"state": "off", "code": 403, "action": "deny", "type": "notFoundCount", "res": "", "ipBlock": "on", "ipBlockTime": 600, "threshold": 30, "duration": 10, "mode": ""}, "methodWhite": {"state": "on", "code": 444, "action": "deny", "type": "methodW<PERSON>e", "res": ""}, "bot": {"state": "", "code": 0, "action": "", "type": "", "res": "", "ipBlock": "", "ipBlockTime": 0, "uri": ""}, "geoRestrict": {"state": "off", "rules": [], "action": "deny", "type": "geoRestrict", "res": "geo"}, "defaultIpBlack": {"state": "on", "code": 403, "action": "deny", "type": "defaultIpBlack", "res": ""}, "xss": {"state": "on", "code": 403, "action": "deny", "type": "xss", "res": ""}, "sql": {"state": "on", "code": 403, "action": "deny", "type": "sql", "res": ""}, "cc": {"state": "off", "code": 0, "action": "deny", "type": "cc", "res": "", "ipBlock": "on", "ipBlockTime": 600, "threshold": 100, "duration": 10, "mode": "uri"}, "urlcc": {"state": "off", "type": "urlcc", "action": "deny", "ipBlock": "on", "ipBlockTime": 600}, "attackCount": {"state": "off", "code": 0, "action": "deny", "type": "attackCount", "res": "", "ipBlock": "on", "ipBlockTime": 3000, "threshold": 10, "duration": 60, "mode": ""}, "fileExt": {"state": "off", "code": 403, "action": "deny", "type": "fileExtCheck", "res": ""}, "cookie": {"state": "on", "code": 403, "action": "deny", "type": "cookie", "res": ""}, "header": {"state": "on", "code": 403, "action": "deny", "type": "header", "res": ""}, "defaultUaBlack": {"state": "on", "code": 403, "action": "deny", "type": "defaultUaBlack", "res": ""}, "defaultUrlBlack": {"state": "on", "code": 403, "action": "deny", "type": "defaultUrlBlack", "res": ""}, "args": {"state": "on", "code": 403, "action": "deny", "type": "args", "res": ""}, "unknownWebsite": {"state": "on", "code": 403, "action": "deny", "type": "unknownWebsite", "res": "unknown"}, "cdn": {"state": "off"}, "vuln": {"state": "off", "code": 403, "action": "deny", "type": "vulnCheck", "res": ""}, "log": {"maxDay": 180, "maxSize": 1, "external": ["acl", "ipWhite", "ipBlack", "geoRestrict", "u<PERSON><PERSON><PERSON><PERSON>", "urlBlack", "u<PERSON><PERSON><PERSON><PERSON>", "uaBlack"], "state": "on"}, "strict": {"state": "off", "code": 403, "action": "deny", "type": "strict", "res": ""}, "app": {"state": "off", "action": "allow", "type": "app", "rule": ""}}