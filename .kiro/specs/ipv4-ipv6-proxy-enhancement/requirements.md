# IPv4+IPv6代理负载均衡增强需求文档

## 介绍

本文档描述了对现有IPv4代理脚本的增强需求，主要目标是在保持客户端使用体验不变的前提下，增加IPv6支持和负载均衡功能。

## 需求

### 需求1：保持客户端使用体验不变

**用户故事：** 作为代理服务的客户，我希望继续使用相同的单一IPv4入口和认证方式，这样我不需要修改现有的客户端配置。

#### 验收标准

1. WHEN 客户使用现有命令 `curl -x "**********************************:18888" ip.sb` THEN 系统 SHALL 正常工作
2. WHEN 客户连接到代理服务 THEN 系统 SHALL 使用相同的端口18888和认证凭据
3. WHEN 客户发起请求 THEN 系统 SHALL 透明地处理IPv4/IPv6负载均衡，客户端无需感知

### 需求2：支持IPv4和IPv6双栈配置

**用户故事：** 作为系统管理员，我希望能够配置代理服务同时支持IPv4和IPv6出站连接，以便充分利用服务器的网络资源。

#### 验收标准

1. WHEN HAVE_IPV6参数设置为true THEN 系统 SHALL 自动检测并使用服务器上的IPv6地址
2. WHEN HAVE_IPV6参数设置为false THEN 系统 SHALL 仅使用IPv4地址进行出站连接
3. WHEN 服务器同时具有IPv4和IPv6地址 THEN 系统 SHALL 能够使用两种协议进行出站连接
4. IF 启用IPv6但服务器没有IPv6地址 THEN 系统 SHALL 回退到仅IPv4模式并给出警告

### 需求3：实现IPv4和IPv6负载均衡

**用户故事：** 作为系统管理员，我希望代理服务能够在多个出站IP地址之间进行负载均衡，以提高网络利用率和避免单点故障。

#### 验收标准

1. WHEN 服务器有1个IPv4和5个IPv6地址 THEN 系统 SHALL 按照IPv4 -> 随机IPv6 -> IPv4 -> 随机IPv6的循环模式进行负载均衡
2. WHEN 进行负载均衡 THEN 系统 SHALL 在IPv4和IPv6之间交替选择
3. WHEN 选择IPv6地址 THEN 系统 SHALL 从可用的IPv6地址中随机选择一个
4. WHEN 选择IPv4地址 THEN 系统 SHALL 从可用的IPv4地址中选择（如果有多个）
5. WHEN 某个IP地址不可用 THEN 系统 SHALL 自动跳过该地址并选择下一个可用地址

### 需求4：配置参数支持

**用户故事：** 作为系统管理员，我希望通过简单的配置参数来控制IPv6功能的启用和禁用。

#### 验收标准

1. WHEN 脚本启动 THEN 系统 SHALL 读取HAVE_IPV6参数（默认值为false）
2. WHEN HAVE_IPV6=true THEN 系统 SHALL 启用IPv6支持和负载均衡
3. WHEN HAVE_IPV6=false THEN 系统 SHALL 仅使用IPv4进行代理
4. WHEN 配置参数无效 THEN 系统 SHALL 使用默认值并给出警告

### 需求5：错误处理和回退机制

**用户故事：** 作为系统管理员，我希望代理服务在遇到网络问题时能够自动处理错误并提供合理的回退机制。

#### 验收标准

1. WHEN IPv6连接失败 THEN 系统 SHALL 自动尝试使用IPv4连接
2. WHEN 当前选择的IP地址不可用 THEN 系统 SHALL 自动选择下一个可用的IP地址
3. WHEN 所有IPv6地址都不可用 THEN 系统 SHALL 回退到仅使用IPv4
4. IF 所有IP地址都不可用 THEN 系统 SHALL 返回适当的错误信息给客户端

### 需求6：日志和监控支持

**用户故事：** 作为系统管理员，我希望能够监控负载均衡的工作状态和IP地址使用情况。

#### 验收标准

1. WHEN 进行负载均衡 THEN 系统 SHALL 记录使用的出站IP地址
2. WHEN IP地址切换 THEN 系统 SHALL 在日志中记录切换信息
3. WHEN 发生错误 THEN 系统 SHALL 记录详细的错误信息和回退操作
4. WHEN 系统启动 THEN 系统 SHALL 记录检测到的IPv4和IPv6地址列表