# IPv4+IPv6代理负载均衡实现任务

- [x] 1. 增强IP地址发现和验证功能
  - 修改`get_ipv6_addresses()`函数，改进IPv6地址发现逻辑
  - 更新`validate_ip_addresses()`函数，增加更严格的IP地址格式验证
  - 添加IP地址可达性测试功能
  - _需求: 2.1, 2.3, 4.1_

- [-] 2. 实现负载均衡核心逻辑
  - [-] 2.1 创建Lua负载均衡模块
    - 编写IP池管理器，支持IPv4和IPv6地址列表
    - 实现轮询+随机混合负载均衡算法
    - 添加IP类型交替选择逻辑（IPv4 -> IPv6 -> IPv4循环）
    - _需求: 3.1, 3.2, 3.3_

  - [ ] 2.2 实现出站IP绑定机制
    - 修改OpenResty配置，支持动态IP绑定
    - 使用`proxy_bind`指令绑定选择的出站IP
    - 处理IPv6地址的特殊格式要求
    - _需求: 3.1, 3.4_

- [ ] 3. 增强错误处理和回退机制
  - [ ] 3.1 实现连接失败重试逻辑
    - 添加连接超时检测
    - 实现自动IP切换机制
    - 记录失败的IP地址并临时跳过
    - _需求: 5.1, 5.2_

  - [ ] 3.2 实现IPv6到IPv4回退机制
    - 检测IPv6连接失败情况
    - 自动回退到IPv4地址池
    - 更新负载均衡状态
    - _需求: 5.1, 5.3_

- [ ] 4. 更新配置生成逻辑
  - [ ] 4.1 修改OpenResty配置模板
    - 更新nginx配置文件模板，集成Lua负载均衡代码
    - 添加`init_by_lua_block`初始化IP池
    - 修改`access_by_lua_block`集成负载均衡选择
    - _需求: 1.1, 2.2_

  - [ ] 4.2 动态生成IP池配置
    - 将发现的IP地址列表嵌入到Lua代码中
    - 根据HAVE_IPV6参数控制IPv6功能启用
    - 生成包含所有IP地址的配置数据结构
    - _需求: 4.2, 4.3_

- [ ] 5. 增强日志和监控功能
  - 修改日志格式，记录使用的出站IP地址和类型
  - 添加负载均衡状态日志
  - 记录IP切换和错误回退信息
  - 增加性能指标记录（连接时间、成功率等）
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [ ] 6. 实现配置参数处理
  - 验证HAVE_IPV6参数的有效性
  - 添加参数默认值处理逻辑
  - 实现配置参数错误处理和警告
  - 更新脚本帮助信息和使用说明
  - _需求: 4.1, 4.4_

- [ ] 7. 创建测试功能
  - [ ] 7.1 实现IP地址发现测试
    - 编写IPv4地址发现测试函数
    - 编写IPv6地址发现测试函数
    - 测试地址验证逻辑的正确性
    - _需求: 2.1, 2.4_

  - [ ] 7.2 实现负载均衡测试
    - 创建负载均衡算法测试
    - 验证IPv4/IPv6交替选择逻辑
    - 测试IPv6随机选择功能
    - 验证边界条件处理
    - _需求: 3.1, 3.2, 3.3_

  - [ ] 7.3 实现代理功能集成测试
    - 测试基本代理功能（保持向后兼容）
    - 验证认证机制正常工作
    - 测试负载均衡在实际请求中的效果
    - 验证客户端使用体验不变
    - _需求: 1.1, 1.2, 1.3_

- [ ] 8. 优化和完善
  - [ ] 8.1 性能优化
    - 优化IP选择算法的性能
    - 减少Lua代码的内存使用
    - 优化配置重载时间
    - _需求: 3.4_

  - [ ] 8.2 错误处理完善
    - 完善所有错误情况的处理逻辑
    - 添加详细的错误信息和建议
    - 实现优雅的服务降级
    - _需求: 5.4_

- [ ] 9. 文档和部署准备
  - 更新脚本使用说明和示例
  - 添加故障排除指南
  - 创建配置验证检查清单
  - 准备部署和回滚程序
  - _需求: 1.1, 4.1_