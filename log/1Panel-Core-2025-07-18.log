[2025-07-18 16:21:45] [INFO] init logger successfully  
[2025-07-18 16:21:45] [INFO] Migration run successfully  
[2025-07-18 16:21:46] [INFO] init session successfully  
[2025-07-18 16:21:46] [INFO] [xpack] migration run successfully  
[2025-07-18 16:21:46] [INFO] [xpack] add health check cron job successful  
[2025-07-18 16:21:46] [INFO] [xpack] add license check cron job successful, handle at 0:32  
[2025-07-18 16:21:46] [INFO] add app store sync cron job successful  
[2025-07-18 16:21:46] [INFO] [offline alert] cron job successful  
[2025-07-18 16:21:46] [DEBUG] tar zxvfC /opt/1panel/tmp/script/scripts.tar.gz /opt/1panel/tmp/script  
[2025-07-18 16:21:46] [INFO] listen at http://0.0.0.0:1443 [tcp4]  
[2025-07-18 16:21:56] [INFO] init logger successfully  
[2025-07-18 16:21:56] [INFO] Migration run successfully  
[2025-07-18 16:21:56] [INFO] init session successfully  
[2025-07-18 16:21:56] [INFO] [xpack] migration run successfully  
[2025-07-18 16:21:56] [INFO] [xpack] add health check cron job successful  
[2025-07-18 16:21:56] [INFO] [xpack] add license check cron job successful, handle at 0:32  
[2025-07-18 16:21:56] [INFO] add app store sync cron job successful  
[2025-07-18 16:21:56] [INFO] [offline alert] cron job successful  
[2025-07-18 16:21:56] [INFO] listen at http://[::]:1443 [tcp]  
[2025-07-18 23:00:00] [ERROR] Error running CustomAppStoreSyncJob: &{}  
[2025-07-18 23:10:41] [INFO] init logger successfully  
[2025-07-18 23:10:41] [INFO] Migration run successfully  
[2025-07-18 23:10:41] [INFO] init session successfully  
[2025-07-18 23:10:41] [INFO] [xpack] migration run successfully  
[2025-07-18 23:10:41] [INFO] [xpack] add health check cron job successful  
[2025-07-18 23:10:41] [INFO] [xpack] add license check cron job successful, handle at 0:32  
[2025-07-18 23:10:41] [INFO] add app store sync cron job successful  
[2025-07-18 23:10:41] [INFO] [offline alert] cron job successful  
[2025-07-18 23:10:41] [INFO] listen at http://[::]:1443 [tcp]  
2025/07/21 14:02:26 httputil: ReverseProxy read error during body copy: read unix @->/etc/1panel/agent.sock: use of closed network connection


[31m2025/07/21 14:02:26 [Recovery] 2025/07/21 - 14:02:26 panic recovered:
POST /api/v2/websites/ssl/search HTTP/1.1
Host: ***************:1443
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: zh
Content-Length: 36
Content-Type: application/json
Cookie: panel_public_key=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF0MXJkVXk3NXhOdDZYRWM0YXZzMgpWQUd0cXpHN1psdHJDWW5YRktDZ0w0MitmVEVwa3d6ODlJeEVmQ0xZenQyQkFncnJPRjltNkZONVV4WHFyaHgrCkUrOWVUcTZiam9OMXRvOFQ3R2JqTlhieDcyVnVZTm0yWi9WRDF0QXVzNHVxY1hRa3liK25FUmt0Q0tlRHZydFIKQllna0N3NHVaS0YyZlMxL3NFaU9lNmJlb2grY1E2M3FUNTFuYTkvdXdtNnBzZk5rWG9XNWc4cGt1S0ZzSnY1RgpYeGlLUTNsaWNEMTJlSHFSS293bmg4R0dTdWEyZ0creXVHd2ZweFRBNVZwVnBtNXZnNVJWeW0wcUtPR21VY3NECkNRMEd5NkhjM21aVDFMbG44V3d3UkhhN1Y3TytxQVlCNG81VDBBb2FETWxla01vYzV3aGZnd1BXYTFFcVRheDQKTFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg%3D%3D; psession=MTc1MzA3Nzc0M3xOd3dBTkRVelFVdENTVUZVVkZwTFNEZGFSRVJDUVZoSFNsTkROVU5XUVRaR1dVTkJWMWxSUTFaYVNsaEpSVlpaTmpJelVVODFRMUU9fKoijmBHfIIccirclsXG1aOj3L4avAitoOd8NVMRAPwq
Currentnode: local
Origin: http://***************:1443
Referer: http://***************:1443/websites/ssl
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********


net/http: abort Handler
net/http/httputil/reverseproxy.go:530 (0x9daf24)
github.com/1Panel-dev/1Panel/core/init/router/proxy.go:59 (0x14c1484)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/bind_domain.go:20 (0x14c0c7a)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/ip_limit.go:22 (0x14c08d5)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/password_expired.go:32 (0x14c06dd)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/loading.go:21 (0x14c0224)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/operation.go:29 (0x14be8ed)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9ed26e)
github.com/gin-gonic/gin@v1.10.0/recovery.go:102 (0x9ed25b)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9ec3a4)
github.com/gin-gonic/gin@v1.10.0/logger.go:249 (0x9ec38b)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9eb86a)
github.com/gin-gonic/gin@v1.10.0/gin.go:677 (0x9eb857)
github.com/gin-gonic/gin@v1.10.0/gin.go:670 (0x9eb3c4)
github.com/gin-gonic/gin@v1.10.0/gin.go:589 (0x9ead31)
net/http/server.go:3210 (0x78de6d)
net/http/server.go:2092 (0x76d38f)
runtime/asm_amd64.s:1700 (0x47c140)
[0m
2025/07/21 14:25:01 httputil: ReverseProxy read error during body copy: read unix @->/etc/1panel/agent.sock: use of closed network connection


[31m2025/07/21 14:25:01 [Recovery] 2025/07/21 - 14:25:01 panic recovered:
POST /api/v2/websites/ssl/search HTTP/1.1
Host: ***************:1443
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: zh
Content-Length: 36
Content-Type: application/json
Cookie: panel_public_key=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF0MXJkVXk3NXhOdDZYRWM0YXZzMgpWQUd0cXpHN1psdHJDWW5YRktDZ0w0MitmVEVwa3d6ODlJeEVmQ0xZenQyQkFncnJPRjltNkZONVV4WHFyaHgrCkUrOWVUcTZiam9OMXRvOFQ3R2JqTlhieDcyVnVZTm0yWi9WRDF0QXVzNHVxY1hRa3liK25FUmt0Q0tlRHZydFIKQllna0N3NHVaS0YyZlMxL3NFaU9lNmJlb2grY1E2M3FUNTFuYTkvdXdtNnBzZk5rWG9XNWc4cGt1S0ZzSnY1RgpYeGlLUTNsaWNEMTJlSHFSS293bmg4R0dTdWEyZ0creXVHd2ZweFRBNVZwVnBtNXZnNVJWeW0wcUtPR21VY3NECkNRMEd5NkhjM21aVDFMbG44V3d3UkhhN1Y3TytxQVlCNG81VDBBb2FETWxla01vYzV3aGZnd1BXYTFFcVRheDQKTFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg%3D%3D; psession=MTc1MzA3OTA5NHxOd3dBTkRVelFVdENTVUZVVkZwTFNEZGFSRVJDUVZoSFNsTkROVU5XUVRaR1dVTkJWMWxSUTFaYVNsaEpSVlpaTmpJelVVODFRMUU9fGqBOqj0RIaBwwssK0g0vwtc9aZEXFn23TMs4lPsCCVB
Currentnode: local
Origin: http://***************:1443
Referer: http://***************:1443/websites
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********


net/http: abort Handler
net/http/httputil/reverseproxy.go:530 (0x9daf24)
github.com/1Panel-dev/1Panel/core/init/router/proxy.go:59 (0x14c1484)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/bind_domain.go:20 (0x14c0c7a)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/ip_limit.go:22 (0x14c08d5)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/password_expired.go:32 (0x14c06dd)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/loading.go:21 (0x14c0224)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9df44a)
github.com/1Panel-dev/1Panel/core/middleware/operation.go:29 (0x14be8ed)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9ed26e)
github.com/gin-gonic/gin@v1.10.0/recovery.go:102 (0x9ed25b)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9ec3a4)
github.com/gin-gonic/gin@v1.10.0/logger.go:249 (0x9ec38b)
github.com/gin-gonic/gin@v1.10.0/context.go:185 (0x9eb86a)
github.com/gin-gonic/gin@v1.10.0/gin.go:677 (0x9eb857)
github.com/gin-gonic/gin@v1.10.0/gin.go:670 (0x9eb3c4)
github.com/gin-gonic/gin@v1.10.0/gin.go:589 (0x9ead31)
net/http/server.go:3210 (0x78de6d)
net/http/server.go:2092 (0x76d38f)
runtime/asm_amd64.s:1700 (0x47c140)
[0m
