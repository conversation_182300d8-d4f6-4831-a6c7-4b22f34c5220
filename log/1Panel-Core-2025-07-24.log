[2025-07-24 00:32:00] [DEBUG] [xpack] sync license license-Panel-dNbaY successful!  
[2025-07-24 00:32:00] [DEBUG] [xpack] sync license license-Panel-jZLUy successful!  
[2025-07-24 00:32:00] [DEBUG] [xpack] sync license license-Panel-trTab successful!  
[2025-07-24 16:07:24] [INFO] start to upgrade now...  
[2025-07-24 16:07:25] [INFO] download all file successful!  
[2025-07-24 16:07:25] [DEBUG] tar zxvfC /opt/1panel/tmp/upgrade/v2.0.5/downloads/1panel-v2.0.5-linux-amd64.tar.gz /opt/1panel/tmp/upgrade/v2.0.5/downloads  
[2025-07-24 16:07:25] [INFO] backup original data successful, now start to upgrade!  
[2025-07-24 16:07:26] [INFO] upgrade successful!  
[2025-07-24 16:07:28] [INFO] init logger successfully  
[2025-07-24 16:07:28] [INFO] Migration run successfully  
[2025-07-24 16:07:28] [INFO] init session successfully  
[2025-07-24 16:07:28] [INFO] [xpack] migration run successfully  
[2025-07-24 16:07:38] [INFO] [xpack] add health check cron job successful  
[2025-07-24 16:07:38] [INFO] [xpack] add license check cron job successful, handle at 0:32  
[2025-07-24 16:07:38] [INFO] add app store sync cron job successful  
[2025-07-24 16:07:38] [INFO] [offline alert] cron job successful  
[2025-07-24 16:07:38] [INFO] add master backup cron job successful  
[2025-07-24 16:07:38] [ERROR] check license failed, message: device id inconsistency., err: 当前环境与许可证导入环境不一致，请编辑许可证重新导入！  
[2025-07-24 16:07:38] [ERROR] check license failed, message: device id inconsistency., err: 当前环境与许可证导入环境不一致，请编辑许可证重新导入！  
[2025-07-24 16:07:38] [INFO] listen at http://[::]:1443 [tcp]  
[2025-07-24 16:07:39] [DEBUG] [xpack] sync license license-Panel-dNbaY successful!  
[2025-07-24 16:18:52] [ERROR] do request to ************** /api/v2/xpack/cluster/ipport/healthy failed, err: <nil>  
[2025-07-24 16:18:57] [ERROR] do request to ************** /api/v2/xpack/cluster/ipport/healthy failed, err: <nil>  
[2025-07-24 16:19:08] [ERROR] do request to ************** /api/v2/xpack/cluster/ipport/healthy failed, err: <nil>  
[2025-07-24 16:34:51] [ERROR] do request to ************* /api/v2/apps/install success but handle failed, err: 服务内部错误: 默认容器网络创建失败！Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?  
[2025-07-24 16:36:28] [INFO] websocket finished  
[2025-07-24 16:36:28] [INFO] websocket finished  
[2025-07-24 16:37:25] [INFO] websocket finished  
[2025-07-24 16:37:25] [INFO] websocket finished  
[2025-07-24 16:39:50] [ERROR] do request to *********** /api/v2/apps/install success but handle failed, err: 服务内部错误: 默认容器网络创建失败！Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?  
[2025-07-24 16:40:36] [INFO] websocket finished  
[2025-07-24 16:40:36] [INFO] websocket finished  
[2025-07-24 16:42:22] [ERROR] do request to *********** /api/v2/apps/install success but handle failed, err: 服务内部错误: 应用名称已存在  
[2025-07-24 17:38:08] [ERROR] [offline alert] failed to request agent, err: client do request failed, err: Post "https://154.222.28.21:1443/api/v2/xpack/alert/offline/logs": context deadline exceeded (Client.Timeout exceeded while awaiting headers), node: hk-crashwork2  
[2025-07-24 21:01:49] [INFO] websocket finished  
[2025-07-24 21:01:49] [INFO] websocket finished  
[2025-07-24 23:00:00] [ERROR] Error running CustomAppStoreSyncJob: &{}  
