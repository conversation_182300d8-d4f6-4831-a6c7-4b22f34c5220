[2025-07-22 00:32:01] [DEBUG] [xpack] sync license license-Panel-jZLUy successful!  
[2025-07-22 00:32:01] [DEBUG] [xpack] sync license license-Panel-dNbaY successful!  
[2025-07-22 00:32:01] [DEBUG] [xpack] sync license license-Panel-trTab successful!  
[2025-07-22 01:41:17] [ERROR] [offline sync] failed to request agent, err: client do request failed, err: Post "https://*************:1443/api/v2/xpack/alert/offline/sync": dial tcp *************:1443: connect: no route to host, node: hk-crashwork1  
[2025-07-22 01:41:18] [ERROR] [offline sync] failed to request agent, err: client do request failed, err: Post "https://*************:1443/api/v2/xpack/alert/offline/sync": dial tcp *************:1443: connect: no route to host, node: hk-crashwork2  
[2025-07-22 04:41:11] [ERROR] [offline alert] failed to request agent, err: client do request failed, err: Post "https://**************:1443/api/v2/xpack/alert/offline/logs": context deadline exceeded (Client.Timeout exceeded while awaiting headers), node: hk-hw  
[2025-07-22 18:11:11] [ERROR] [offline alert] failed to request agent, err: client do request failed, err: Post "https://*************:1443/api/v2/xpack/alert/offline/logs": context deadline exceeded (Client.Timeout exceeded while awaiting headers), node: hk-crashwork1  
[2025-07-22 23:00:00] [ERROR] Error running CustomAppStoreSyncJob: &{}  
