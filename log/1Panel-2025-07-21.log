[2025-07-21 00:00:00] [INFO] clear waf log task in progress ...  
[2025-07-21 00:00:00] [INFO] clear monitor website log  task has completed  
[2025-07-21 00:00:00] [INFO] clear waf log  task has completed  
[2025-07-21 00:00:00] [INFO] Website scheduled task in progress ...  
[2025-07-21 00:00:00] [INFO] Website scheduled task has completed  
[2025-07-21 00:00:00] [INFO] The scheduled certificate update task is currently in progress ...  
[2025-07-21 00:00:00] [INFO] The scheduled certificate update task has completed  
[2025-07-21 02:56:00] [INFO] AppStore scheduled task in progress ...  
[2025-07-21 02:56:00] [INFO] AppStore scheduled task has completed  
[2025-07-21 02:56:00] [INFO] [AppStore] download file from https://apps-assets.fit2cloud.com/stable/1panel.json.zip  
[2025-07-21 11:52:55] [ERROR] load service ssl from setting failed, err: record not found  
[2025-07-21 11:56:46] [INFO] docker-compose.yml /opt/1panel/docker/compose/sub-check/docker-compose.yaml has been replaced, now start to docker-compose restart  
[2025-07-21 11:56:46] [INFO] 1panel.env file successfully created or updated with env variables in /opt/1panel/docker/compose/sub-check/1panel.env  
[2025-07-21 11:57:05] [INFO] start container subs-check operation restart  
[2025-07-21 12:08:44] [INFO] start container subs-check operation restart  
[2025-07-21 12:13:37] [INFO] start container subs-check operation restart  
[2025-07-21 12:15:25] [INFO] start container subs-check operation restart  
[2025-07-21 13:36:28] [INFO] start container subs-check operation restart  
[2025-07-21 13:43:00] [INFO] start container subs-check operation restart  
[2025-07-21 14:00:16] [INFO] start container subs-check operation restart  
[2025-07-21 14:03:14] [ERROR] load service ssl from setting failed, err: record not found  
[2025-07-21 14:10:01] [INFO] start container subs-check operation restart  
[2025-07-21 14:12:35] [INFO] start container subs-check operation restart  
[2025-07-21 14:36:47] [INFO] start container subs-check operation restart  
[2025-07-21 14:45:45] [INFO] start container subs-check operation restart  
[2025-07-21 19:34:07] [ERROR] reading webSocket message failed, err: websocket: close 1005 (no status)  
[2025-07-21 19:34:07] [DEBUG] thread of receive ws msg has exited now  
[2025-07-21 19:34:07] [INFO] websocket finished  
[2025-07-21 19:34:07] [ERROR] ssh session wait failed, err: signal: killed  
[2025-07-21 19:34:07] [DEBUG] thread of handle slave event has exited now  
[2025-07-21 20:55:03] [ERROR] reading webSocket message failed, err: websocket: close 1005 (no status)  
[2025-07-21 20:55:03] [DEBUG] thread of receive ws msg has exited now  
[2025-07-21 20:55:03] [INFO] websocket finished  
[2025-07-21 20:55:03] [ERROR] ssh session wait failed, err: signal: killed  
[2025-07-21 20:55:03] [DEBUG] thread of handle slave event has exited now  
