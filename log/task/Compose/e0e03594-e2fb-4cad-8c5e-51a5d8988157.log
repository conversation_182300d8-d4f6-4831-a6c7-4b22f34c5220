2025/07/21 23:34:27 创建编排 [http_proxy_pool] 任务开始 [START]
2025/07/21 23:47:07 编排创建输出： http_proxy_pool Pulling 
 http_proxy_pool Warning pull access denied for http_proxy_pool, repository does not exist or may require 'docker login': denied: requested access to the resource is denied
#1 [internal] load local bake definitions
#1 reading from stdin 413B done
#1 DONE 0.0s

#2 [internal] load build definition from Dockerfile
#2 transferring dockerfile: 1.14kB done
#2 DONE 0.0s

#3 [internal] load metadata for docker.io/library/python:3.11-alpine
#3 DONE 3.1s

#4 [internal] load .dockerignore
#4 transferring context: 476B done
#4 DONE 0.0s

#5 [internal] load build context
#5 transferring context: 137.58kB 0.0s done
#5 DONE 0.0s

#6 [build 1/3] FROM docker.io/library/python:3.11-alpine@sha256:9ce54d7ed458f71129c977478dd106cf6165a49b73fa38c217cc54de8f3e2bd0
#6 resolve docker.io/library/python:3.11-alpine@sha256:9ce54d7ed458f71129c977478dd106cf6165a49b73fa38c217cc54de8f3e2bd0 0.0s done
#6 sha256:7c0a09ba434eb103080e7a5a56750954d4a0661f1c69e098a842cf644e1a3d08 0B / 16.23MB 0.1s
#6 sha256:7eb858211fa96da06350778902fca7b9fc5cd2eb96a358b45dbf207897a032b3 0B / 248B 0.1s
#6 sha256:9ce54d7ed458f71129c977478dd106cf6165a49b73fa38c217cc54de8f3e2bd0 10.30kB / 10.30kB done
#6 sha256:54469357443208c20e58bc68d29622d9ea8712f23becb9e35194aef431284f0d 1.74kB / 1.74kB done
#6 sha256:cb0ca8fd2c961d714de1c6b269c3750ce39da716f8399499e4139c0d6d004d80 5.16kB / 5.16kB done
#6 sha256:e48fefb90869c01b3a471d1728670ad2acb2c0d970c6d26ad055a0931040189d 0B / 447.74kB 0.1s
#6 sha256:7c0a09ba434eb103080e7a5a56750954d4a0661f1c69e098a842cf644e1a3d08 4.19MB / 16.23MB 0.6s
#6 sha256:7eb858211fa96da06350778902fca7b9fc5cd2eb96a358b45dbf207897a032b3 248B / 248B 0.6s done
#6 sha256:7c0a09ba434eb103080e7a5a56750954d4a0661f1c69e098a842cf644e1a3d08 16.23MB / 16.23MB 0.7s done
#6 sha256:e48fefb90869c01b3a471d1728670ad2acb2c0d970c6d26ad055a0931040189d 447.74kB / 447.74kB 1.1s done
#6 extracting sha256:e48fefb90869c01b3a471d1728670ad2acb2c0d970c6d26ad055a0931040189d 0.1s done
#6 extracting sha256:7c0a09ba434eb103080e7a5a56750954d4a0661f1c69e098a842cf644e1a3d08
#6 extracting sha256:7c0a09ba434eb103080e7a5a56750954d4a0661f1c69e098a842cf644e1a3d08 0.9s done
#6 extracting sha256:7eb858211fa96da06350778902fca7b9fc5cd2eb96a358b45dbf207897a032b3 done
#6 DONE 2.2s

#7 [build 2/3] COPY pyproject.toml .
#7 DONE 0.1s

#8 [build 3/3] RUN apk update &&     apk add --no-cache gcc g++ libffi-dev openssl-dev libxml2-dev libxslt-dev build-base musl-dev &&     pip install -U pip &&     pip install --timeout 30 --user --no-cache-dir --no-warn-script-location -e .
#8 0.208 fetch https://dl-cdn.alpinelinux.org/alpine/v3.22/main/x86_64/APKINDEX.tar.gz
#8 0.613 fetch https://dl-cdn.alpinelinux.org/alpine/v3.22/community/x86_64/APKINDEX.tar.gz
#8 1.196 v3.22.1-51-g1f0d0757887 [https://dl-cdn.alpinelinux.org/alpine/v3.22/main]
#8 1.196 v3.22.1-52-gd7abf048718 [https://dl-cdn.alpinelinux.org/alpine/v3.22/community]
#8 1.196 OK: 26315 distinct packages available
#8 1.312 fetch https://dl-cdn.alpinelinux.org/alpine/v3.22/main/x86_64/APKINDEX.tar.gz
#8 1.634 fetch https://dl-cdn.alpinelinux.org/alpine/v3.22/community/x86_64/APKINDEX.tar.gz
#8 2.119 (1/33) Installing libgcc (14.2.0-r6)
#8 2.161 (2/33) Installing jansson (2.14.1-r0)
#8 2.205 (3/33) Installing libstdc++ (14.2.0-r6)
#8 2.281 (4/33) Installing zstd-libs (1.5.7-r0)
#8 2.331 (5/33) Installing binutils (2.44-r2)
#8 2.474 (6/33) Installing libmagic (5.46-r2)
#8 2.553 (7/33) Installing file (5.46-r2)
#8 2.594 (8/33) Installing libgomp (14.2.0-r6)
#8 2.637 (9/33) Installing libatomic (14.2.0-r6)
#8 2.677 (10/33) Installing gmp (6.3.0-r3)
#8 2.722 (11/33) Installing isl26 (0.26-r1)
#8 2.779 (12/33) Installing mpfr4 (4.2.1_p1-r0)
#8 2.826 (13/33) Installing mpc1 (1.3.1-r1)
#8 2.866 (14/33) Installing gcc (14.2.0-r6)
#8 3.995 (15/33) Installing libstdc++-dev (14.2.0-r6)
#8 4.241 (16/33) Installing musl-dev (1.2.5-r10)
#8 4.358 (17/33) Installing g++ (14.2.0-r6)
#8 4.737 (18/33) Installing make (4.4.1-r3)
#8 4.779 (19/33) Installing fortify-headers (1.1-r5)
#8 4.820 (20/33) Installing patch (2.8-r0)
#8 4.861 (21/33) Installing build-base (0.5-r3)
#8 4.861 (22/33) Installing linux-headers (6.14.2-r0)
#8 5.062 (23/33) Installing pkgconf (2.4.3-r0)
#8 5.104 (24/33) Installing libffi-dev (3.4.8-r0)
#8 5.145 (25/33) Installing zlib-dev (1.3.1-r2)
#8 5.189 (26/33) Installing xz (5.8.1-r0)
#8 5.233 (27/33) Installing xz-dev (5.8.1-r0)
#8 5.276 (28/33) Installing libxml2 (2.13.8-r0)
#8 5.327 (29/33) Installing libxml2-utils (2.13.8-r0)
#8 5.368 (30/33) Installing libxml2-dev (2.13.8-r0)
#8 5.416 (31/33) Installing libxslt (1.1.43-r3)
#8 5.458 (32/33) Installing libxslt-dev (1.1.43-r3)
#8 5.502 (33/33) Installing openssl-dev (3.5.1-r0)
#8 5.597 Executing busybox-1.37.0-r18.trigger
#8 5.612 OK: 261 MiB in 71 packages
#8 8.782 Requirement already satisfied: pip in /usr/local/lib/python3.11/site-packages (24.0)
#8 9.031 Collecting pip
#8 9.170   Downloading pip-25.1.1-py3-none-any.whl.metadata (3.6 kB)
#8 9.221 Downloading pip-25.1.1-py3-none-any.whl (1.8 MB)
#8 9.448    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.8/1.8 MB 8.3 MB/s eta 0:00:00
#8 9.516 Installing collected packages: pip
#8 9.516   Attempting uninstall: pip
#8 9.522     Found existing installation: pip 24.0
#8 9.619     Uninstalling pip-24.0:
#8 9.916       Successfully uninstalled pip-24.0
#8 11.68 Successfully installed pip-25.1.1
#8 11.69 WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
#8 12.52 Obtaining file:///
#8 12.53   Installing build dependencies: started
#8 14.33   Installing build dependencies: finished with status 'done'
#8 14.33   Checking if build backend supports build_editable: started
#8 14.82   Checking if build backend supports build_editable: finished with status 'done'
#8 14.82   Getting requirements to build editable: started
#8 750.8   Getting requirements to build editable: finished with status 'error'
#8 750.8   error: subprocess-exited-with-error
#8 750.8   
#8 750.8   × Getting requirements to build editable did not run successfully.
#8 750.8   │ exit code: -9
#8 750.8   ╰─> [0 lines of output]
#8 750.8       [end of output]
#8 750.8   
#8 750.8   note: This error originates from a subprocess, and is likely not a problem with pip.
#8 751.0 error: subprocess-exited-with-error
#8 751.0 
#8 751.0 × Getting requirements to build editable did not run successfully.
#8 751.0 │ exit code: -9
#8 751.0 ╰─> See above for output.
#8 751.0 
#8 751.0 note: This error originates from a subprocess, and is likely not a problem with pip.
#8 ERROR: process "/bin/sh -c apk update &&     apk add --no-cache gcc g++ libffi-dev openssl-dev libxml2-dev libxslt-dev build-base musl-dev &&     pip install -U pip &&     pip install --timeout 30 --user --no-cache-dir --no-warn-script-location -e ." did not complete successfully: exit code: 1
------
 > [build 3/3] RUN apk update &&     apk add --no-cache gcc g++ libffi-dev openssl-dev libxml2-dev libxslt-dev build-base musl-dev &&     pip install -U pip &&     pip install --timeout 30 --user --no-cache-dir --no-warn-script-location -e .:
750.8       [end of output]
750.8   
750.8   note: This error originates from a subprocess, and is likely not a problem with pip.
751.0 error: subprocess-exited-with-error
751.0 
751.0 × Getting requirements to build editable did not run successfully.
751.0 │ exit code: -9
751.0 ╰─> See above for output.
751.0 
751.0 note: This error originates from a subprocess, and is likely not a problem with pip.
------
Dockerfile:8

--------------------

   7 |     # 安装构建依赖和Python包

   8 | >>> RUN apk update && \

   9 | >>>     apk add --no-cache gcc g++ libffi-dev openssl-dev libxml2-dev libxslt-dev build-base musl-dev && \

  10 | >>>     pip install -U pip && \

  11 | >>>     pip install --timeout 30 --user --no-cache-dir --no-warn-script-location -e .

  12 |     

--------------------

failed to solve: process "/bin/sh -c apk update &&     apk add --no-cache gcc g++ libffi-dev openssl-dev libxml2-dev libxslt-dev build-base musl-dev &&     pip install -U pip &&     pip install --timeout 30 --user --no-cache-dir --no-warn-script-location -e ." did not complete successfully: exit code: 1

2025/07/21 23:47:07 创建编排 失败: exit status 1
2025/07/21 23:47:08 [TASK-END]
