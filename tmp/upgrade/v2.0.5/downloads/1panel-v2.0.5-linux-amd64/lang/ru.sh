#!/bin/bash

TXT_START_INSTALLATION="======================= Начало установки ======================="
TXT_RUN_AS_ROOT="Пожалуйста, запустите этот скрипт от имени root или с правами sudo"
TXT_SUCCESS_MESSAGE="Успешно"
TXT_SUCCESSFULLY_MESSAGE="Успешно выполнено"
TXT_FAIELD_MESSAGE="Ошибка"
TXT_IGNORE_MESSAGE="Пропустить"
TXT_SKIP_MESSAGE="Пропустить"
TXT_PANEL_ALREADY_INSTALLED="Панель управления 1Panel уже установлена, повторная установка невозможна"
TXT_SET_INSTALL_DIR="Укажите директорию для установки 1Panel (по умолчанию /opt): "
TXT_PROVIDE_FULL_PATH="Пожалуйста, укажите полный путь к директории"
TXT_SELECTED_INSTALL_PATH="Выбранный путь установки"
TXT_TIMEOUT_USE_DEFAULT_PATH="(Время ожидания истекло, используется путь по умолчанию /opt)"
TXT_CREATE_NEW_CONFIG="Создание нового конфигурационного файла"
TXT_ACCELERATION_CONFIG_ADDED="Конфигурация ускорения образов добавлена."
TXT_USING_TENCENT_MIRROR="Добавлена конфигурация ускорения образов Tencent Cloud."
TXT_ACCELERATION_CONFIG_NOT="Ускорение образов не настроено."
TXT_ACCELERATION_CONFIG_ADD="Хотите настроить ускорение образов (y/n): "
TXT_ACCELERATION_CONFIG_EXISTS="Конфигурационный файл уже существует, будет создана резервная копия: "
TXT_RESTARTING_DOCKER="Перезапуск службы Docker..."
TXT_DOCKER_RESTARTED="Служба Docker успешно перезапущена."
TXT_INSTALL_DOCKER_CONFIRM="Docker не установлен. Установить сейчас? [y/n]: "
TXT_CANCEL_INSTALL_DOCKER="Установка Docker отменена"
TXT_INVALID_YN_INPUT="Недопустимый ввод. Пожалуйста, введите y или n"
TXT_DOCKER_INSTALL_ONLINE="... Установка Docker онлайн"
TXT_ACCELERATOR_NOT_CONFIGURED="Ускорение образов не настроено."
TXT_LOW_DOCKER_VERSION="Обнаружена версия Docker ниже 20.x. Рекомендуется обновить вручную во избежание ограничений функциональности."
TXT_INSTALL_DOCKER_ONLINE="... Установка Docker онлайн"
TXT_DOWNLOAD_DOCKER_SCRIPT_FAIL="Не удалось загрузить скрипт установки из"
TXT_DOWNLOAD_DOCKER_SCRIPT="загрузка скрипта установки docker"
TXT_DOWNLOAD_DOCKER_SCRIPT_SUCCESS="Docker загружен из"
TXT_TRY_NEXT_LINK="пробуем следующую альтернативную ссылку"
TXT_DOWNLOAD_FAIELD="Не удалось загрузить скрипт установки из"
TXT_ALL_DOWNLOAD_ATTEMPTS_FAILED="Все попытки загрузки не удались. Попробуйте установить Docker вручную, выполнив следующую команду:"
TXT_REGIONS_OTHER_THAN_CHINA="Нет необходимости менять источник"
TXT_DOCKER_INSTALL_SUCCESS="Docker успешно установлен"
TXT_DOCKER_INSTALL_FAIL="Установка Docker не удалась\nВы можете попробовать установить Docker, используя оффлайн-пакет, обратитесь к следующей ссылке для подробных инструкций: https://docs.1panel.hk/installation/"
TXT_CHOOSE_LOWEST_LATENCY_SOURCE="Выберите источник с наименьшей задержкой"
TXT_CHOOSE_LOWEST_LATENCY_DELAY="Задержка (в секундах)"
TXT_CANNOT_SELECT_SOURCE="Невозможно выбрать источник для установки"
TXT_DOCKER_START_NOTICE="... запуск docker"
TXT_DOCKER_MAY_EFFECT_STORE="что может повлиять на нормальную работу App Store."
TXT_SET_PANEL_PORT="Установите порт 1Panel (по умолчанию"
TXT_INPUT_PORT_NUMBER="Ошибка: Введенный номер порта должен быть от 1 до 65535"
TXT_THE_PORT_U_SET="Установленный порт: "
TXT_PORT_OCCUPIED="Порт занят, пожалуйста, введите другой..."
TXT_FIREWALL_OPEN_PORT="Открытие порта в брандмауэре"
TXT_FIREWALL_NOT_ACTIVE_SKIP="Брандмауэр не активен, пропускаем открытие порта"
TXT_FIREWALL_NOT_ACTIVE_IGNORE="Брандмауэр не активен, игнорируем открытие порта"
TXT_SET_PANEL_ENTRANCE="Установите безопасный вход 1Panel (по умолчанию"
TXT_INPUT_ENTRANCE_RULE="Ошибка: Безопасный вход панели поддерживает только буквы, цифры, подчеркивания, длина 3-30 символов"
TXT_YOUR_PANEL_ENTRANCE="Установленный безопасный вход панели"
TXT_SET_PANEL_USER="Установите пользователя панели 1Panel (по умолчанию"
TXT_INPUT_USERNAME_RULE="Ошибка: Имя пользователя панели поддерживает только буквы, цифры, подчеркивания, длина 3-30 символов"
TXT_YOUR_PANEL_USERNAME="Установленное имя пользователя панели"
TXT_SET_PANEL_PASSWORD="Установите пароль панели 1Panel, нажмите Enter после установки (по умолчанию"
TXT_INPUT_PASSWORD_RULE="Ошибка: Пароль панели поддерживает только буквы, цифры, специальные символы (!@#$%*_,.?), длина 8-30 символов"
TXT_CONFIGURE_PANEL_SERVICE="Настройка службы 1Panel"
TXT_START_PANEL_SERVICE="Запуск службы 1Panel"
TXT_PANEL_SERVICE_START_SUCCESS="Служба 1Panel успешно запущена. Продолжается выполнение последующих настроек, пожалуйста, подождите..."
TXT_PANEL_SERVICE_START_ERROR="Ошибка запуска службы 1Panel!"
TXT_THANK_YOU_WAITING="=================Спасибо за ожидание, установка завершена=================="
TXT_BROWSER_ACCESS_PANEL="Пожалуйста, войдите в панель через браузер:"
TXT_EXTERNAL_ADDRESS="Внешний адрес:"
TXT_INTERNAL_ADDRESS="Внутренний адрес:"
TXT_PANEL_USER="Пользователь панели:"
TXT_PANEL_PASSWORD="Пароль панели:"
TXT_PROJECT_OFFICIAL_WEBSITE="Официальный сайт: https://1panel.hk"
TXT_PROJECT_DOCUMENTATION="Документация проекта: https://docs.1panel.hk"
TXT_PROJECT_REPOSITORY="Репозиторий кода: https://github.com/1Panel-dev/1Panel"
TXT_COMMUNITY="Присоединяйтесь к сообществу 1Panel в Discord для поддержки и обсуждений: https://discord.gg/bUpUqWqdRr"
TXT_OPEN_PORT_SECURITY_GROUP="Если вы используете облачный сервер, пожалуйста, откройте порт в группе безопасности"
TXT_REMEMBER_YOUR_PASSWORD="Для безопасности вашего сервера вы не сможете увидеть свой пароль после выхода с этого экрана, пожалуйста, запомните ваш пароль."
TXT_PANEL_SERVICE_STATUS="Проверить статус службы 1Panel"
TXT_PANEL_SERVICE_RESTART="Перезапустить службу 1Panel"
TXT_PANEL_SERVICE_STOP="Остановить службу 1Panel"
TXT_PANEL_SERVICE_START="Запустить службу 1Panel"
TXT_PANEL_SERVICE_UNINSTALL="Удалить службу 1Panel"
TXT_PANEL_SERVICE_USER_INFO="Получить информацию о пользователе 1Panel"
TXT_PANEL_SERVICE_LISTEN_IP="Переключить IP прослушивания 1Panel"
TXT_PANEL_SERVICE_VERSION="Получить информацию о версии 1Panel"
TXT_PANEL_SERVICE_UPDATE="Обновить систему 1Panel"
TXT_PANEL_SERVICE_RESET="Сбросить систему 1Panel"
TXT_PANEL_SERVICE_RESTORE="Восстановить систему 1Panel"
TXT_PANEL_SERVICE_UNINSTALL_NOTICE="Удаление остановит и удалит службу 1Panel. Продолжить? [y/n]: "
TXT_PANEL_SERVICE_UNINSTALL_START="Начало удаления панели управления сервером 1Panel для Linux"
TXT_PANEL_SERVICE_UNINSTALL_STOP="Остановка процессов службы 1Panel"
TXT_PANEL_SERVICE_UNINSTALL_REMOVE="Удаление исполняемых файлов 1Panel и связанных конфигураций службы"
TXT_PANEL_SERVICE_UNINSTALL_REMOVE_CONFIG="Перезагрузка конфигурационных файлов службы"
TXT_PANEL_SERVICE_UNINSTALL_REMOVE_SUCCESS="Удаление завершено!"
TXT_PANEL_DATA_KEEP_PROMPT="Удалить каталог данных 1Panel? [y/n]: "
TXT_PANEL_DATA_KEEP="Сохранить каталог данных 1Panel"
TXT_PANEL_DATA_DELETE="Удалить каталог данных 1Panel"
TXT_PANEL_SERVICE_RESTORE_NOTICE="1Panel будет восстановлен до последней стабильной версии. Хотите продолжить? [y/n]: "
TXT_PANEL_SERVICE_UNSUPPORTED_PARAMETER="Неподдерживаемые параметры, используйте help или --help для получения справки"
TXT_PANEL_CONTROL_SCRIPT="Скрипт управления 1Panel"
TXT_LANG_SELECTED_MSG="Язык уже выбран: "
TXT_LANG_PROMPT_MSG="Выберите язык:"
TXT_LANG_CHOICE_MSG="Введите номер, соответствующий вашему выбору языка: "
TXT_LANG_SELECTED_CONFIRM_MSG="Вы выбрали: "
TXT_LANG_INVALID_MSG="Неверный выбор. Используется английский язык (en) по умолчанию."
TXT_LANG_NOT_FOUND_MSG="Языковой файл не найден:"
TXT_PANEL_SERVICE_REQUIRE_CORE_OR_AGENT="Пожалуйста, укажите: core или agent"
TXT_PANEL_SERVICE_REQUIRE_CORE_AGENT_OR_ALL="Пожалуйста, укажите: core, agent или all"
