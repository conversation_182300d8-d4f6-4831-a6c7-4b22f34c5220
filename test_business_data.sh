#!/bin/bash

# ===================================================================
# 业务数据获取测试脚本 - 东方财富股票数据
# ===================================================================

# 配置参数
PROXY_HOST="***************"
PROXY_PORT="18888"
PROXY_USER="proxy"
PROXY_PASS="Yyh275822746Xyz"
PROXY_URL="http://${PROXY_USER}:${PROXY_PASS}@${PROXY_HOST}:${PROXY_PORT}"

# 东方财富API URL
EASTMONEY_API="https://push2his.eastmoney.com/api/qt/stock/kline/get?fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f116&ut=7eea3edcaed734bea9cbfc24409ed989&klt=101&fqt=0&secid=1.000001&beg=20250102&end=20250103"

echo "=========================================="
echo "🧪 业务数据获取测试 - 东方财富股票API"
echo "=========================================="
echo "代理地址: ${PROXY_HOST}:${PROXY_PORT}"
echo "测试API: 东方财富股票K线数据"
echo "股票代码: 000001 (上证指数)"
echo "时间范围: 2025-01-02 到 2025-01-03"
echo ""

# 记录测试结果
declare -A ip_usage
total_requests=0
successful_requests=0
failed_requests=0

echo "开始进行20次业务数据获取测试..."
echo "每次请求都会记录使用的出站IP地址和响应状态"
echo ""

for i in {1..20}; do
    total_requests=$((total_requests + 1))
    
    echo -n "请求 $i: "
    
    # 发起请求，获取HTTP状态码和响应时间
    start_time=$(date +%s%3N)
    
    # 使用curl获取状态码，同时尝试获取响应内容的一部分来判断成功
    response=$(curl -x "$PROXY_URL" \
        -s \
        -w "HTTP_CODE:%{http_code}|TIME:%{time_total}|IP:%{local_ip}" \
        --connect-timeout 15 \
        --max-time 30 \
        "$EASTMONEY_API" 2>/dev/null)
    
    curl_exit_code=$?
    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))
    
    if [ $curl_exit_code -eq 0 ]; then
        # 解析curl的输出
        http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
        time_total=$(echo "$response" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
        local_ip=$(echo "$response" | grep -o "IP:[^|]*" | cut -d: -f2)
        
        # 获取实际的响应内容（去掉curl的统计信息）
        content=$(echo "$response" | sed 's/HTTP_CODE:[^|]*|TIME:[^|]*|IP:[^|]*$//')
        
        if [ "$http_code" = "200" ]; then
            successful_requests=$((successful_requests + 1))
            
            # 判断IP类型
            if [[ $local_ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
                ip_type="IPv4"
            elif [[ $local_ip =~ ^[0-9a-fA-F:]+$ ]]; then
                ip_type="IPv6"
            else
                ip_type="Unknown"
            fi
            
            # 记录IP使用情况
            ip_key="${ip_type}:${local_ip}"
            ip_usage["$ip_key"]=$((${ip_usage["$ip_key"]} + 1))
            
            # 检查响应内容是否包含预期的数据
            if echo "$content" | grep -q '"klines"' && echo "$content" | grep -q '"data"'; then
                echo "✅ 成功 (${http_code}) - ${ip_type} ${local_ip} - ${time_total}s - 数据完整"
            else
                echo "⚠️  成功 (${http_code}) - ${ip_type} ${local_ip} - ${time_total}s - 数据异常"
            fi
        else
            failed_requests=$((failed_requests + 1))
            echo "❌ HTTP错误 (${http_code}) - ${local_ip} - ${time_total}s"
        fi
    else
        failed_requests=$((failed_requests + 1))
        echo "❌ 连接失败 (curl错误码: $curl_exit_code) - ${duration}ms"
    fi
    
    # 短暂延迟，让负载均衡生效
    sleep 1
done

echo ""
echo "=========================================="
echo "📊 业务数据获取测试结果"
echo "=========================================="
echo "总请求数: $total_requests"
echo "成功请求数: $successful_requests"
echo "失败请求数: $failed_requests"
echo "成功率: $(( successful_requests * 100 / total_requests ))%"
echo ""

echo "出站IP使用统计:"
if [ ${#ip_usage[@]} -eq 0 ]; then
    echo "  无IP使用记录"
else
    ipv4_total=0
    ipv6_total=0
    
    for ip_info in "${!ip_usage[@]}"; do
        count=${ip_usage[$ip_info]}
        echo "  $ip_info: $count 次"
        
        if [[ $ip_info == IPv4:* ]]; then
            ipv4_total=$((ipv4_total + count))
        elif [[ $ip_info == IPv6:* ]]; then
            ipv6_total=$((ipv6_total + count))
        fi
    done
    
    echo ""
    echo "IP类型使用统计:"
    echo "  IPv4总使用次数: $ipv4_total"
    echo "  IPv6总使用次数: $ipv6_total"
    
    if [ $ipv4_total -gt 0 ] && [ $ipv6_total -gt 0 ]; then
        echo "  ✅ 负载均衡正常 - IPv4和IPv6都有使用"
        echo "  📊 IPv4使用比例: $(( ipv4_total * 100 / (ipv4_total + ipv6_total) ))%"
        echo "  📊 IPv6使用比例: $(( ipv6_total * 100 / (ipv4_total + ipv6_total) ))%"
    elif [ $ipv4_total -gt 0 ]; then
        echo "  ⚠️  仅使用IPv4 - 可能IPv6不可用"
    elif [ $ipv6_total -gt 0 ]; then
        echo "  ⚠️  仅使用IPv6 - 异常情况"
    fi
fi

echo ""

# 业务可用性评估
if [ $successful_requests -gt 0 ]; then
    echo "业务可用性评估:"
    echo "  ✅ 代理服务可正常用于业务数据获取"
    echo "  ✅ 东方财富API访问正常"
    echo "  ✅ 负载均衡功能在业务场景下工作正常"
    
    if [ $successful_requests -eq $total_requests ]; then
        echo "  🎉 完美！所有请求都成功"
    else
        echo "  ⚠️  部分请求失败，建议检查网络连接或API限制"
    fi
else
    echo "业务可用性评估:"
    echo "  ❌ 代理服务无法用于业务数据获取"
    echo "  ❌ 建议检查代理配置和网络连接"
fi

echo ""
echo "=========================================="
echo "🎉 业务数据获取测试完成"
echo "=========================================="