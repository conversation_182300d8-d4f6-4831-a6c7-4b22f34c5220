#!/bin/bash

# ===================================================================
# IP地址发现功能测试脚本 - 极简版
# ===================================================================

# 获取公网IPv4地址 - 极简实现
get_ipv4_addresses() {
    for service in "ip.sb" "ipinfo.io/ip" "icanhazip.com"; do
        local ipv4=$(curl -4 -s --connect-timeout 5 --max-time 10 "$service" 2>/dev/null | head -1)
        if [ -n "$ipv4" ]; then
            echo "$ipv4"
            return
        fi
    done
    echo ""
}

# 获取公网IPv6地址 - 极简实现
get_ipv6_addresses() {
    if [ "$HAVE_IPV6" != "true" ]; then
        echo ""
        return
    fi
    
    for service in "ip.sb" "ipv6.icanhazip.com"; do
        local ipv6=$(curl -6 -s --connect-timeout 5 --max-time 10 "$service" 2>/dev/null | head -1)
        if [ -n "$ipv6" ]; then
            echo "$ipv6"
            return
        fi
    done
    echo ""
}

echo "=========================================="
echo "🧪 IP地址发现功能测试 - 极简版"
echo "=========================================="

# 测试IPv4地址发现
echo "1. 测试IPv4地址发现..."
HAVE_IPV6=false
ipv4_result=$(get_ipv4_addresses)
if [ -n "$ipv4_result" ]; then
    echo "✅ IPv4地址发现成功: $ipv4_result"
else
    echo "❌ IPv4地址发现失败"
fi

echo ""

# 测试IPv6地址发现
echo "2. 测试IPv6地址发现..."
HAVE_IPV6=true
ipv6_result=$(get_ipv6_addresses)
if [ -n "$ipv6_result" ]; then
    echo "✅ IPv6地址发现成功: $ipv6_result"
else
    echo "ℹ️  IPv6地址发现结果为空"
fi

echo ""

# 测试严格验证逻辑
echo "3. 测试严格验证逻辑..."

echo "3.1 测试IPv4必须存在的验证..."
if [ -n "$ipv4_result" ]; then
    echo "✅ IPv4地址存在，验证通过"
else
    echo "❌ IPv4地址不存在，应该报错"
fi

echo "3.2 测试IPv6严格验证..."
HAVE_IPV6=true
if [ "$HAVE_IPV6" = "true" ] && [ -z "$ipv6_result" ]; then
    echo "❌ 启用IPv6但未找到IPv6地址，应该报错退出"
elif [ "$HAVE_IPV6" = "true" ] && [ -n "$ipv6_result" ]; then
    echo "✅ 启用IPv6且找到IPv6地址，验证通过"
else
    echo "ℹ️  IPv6未启用，跳过验证"
fi

echo ""

# 测试负载均衡策略显示
echo "4. 测试负载均衡策略..."
if [ -n "$ipv4_result" ] && [ -n "$ipv6_result" ]; then
    ipv4_count=1  # 极简版每次只返回一个IP
    ipv6_count=1
    echo "ℹ️  负载均衡策略: IPv4($ipv4_count个) ⟷ IPv6($ipv6_count个) 交替使用"
    echo "✅ 负载均衡策略显示正常"
else
    echo "ℹ️  仅IPv4模式，无负载均衡"
fi

echo ""
echo "=========================================="
echo "🎉 IP地址发现功能测试完成"
echo "=========================================="