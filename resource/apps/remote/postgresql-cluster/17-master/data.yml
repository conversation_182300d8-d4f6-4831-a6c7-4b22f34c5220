additionalProperties:
  formFields:
    - default: postgres
      envKey: PANEL_DB_ROOT_USER
      label:
        en: Username
        ja: ユーザー名
        ms: <PERSON>a pengguna
        pt-br: Nome de usuário
        ru: Имя пользователя
        ko: 사용자 이름
        zh-hant: 用戶名
        zh: 用户名
      required: true
      type: text
      disabled: true
    - default: password
      envKey: PANEL_DB_ROOT_PASSWORD
      label:
        en: Password
        ja: パスワード
        ms: <PERSON><PERSON> la<PERSON>an
        pt-br: Senha
        ru: Пароль
        ko: 비밀번호
        zh-hant: 密碼
        zh: 密码
      random: true
      required: true
      type: password
    - default: replica_user
      envKey: REPLICATION_USER
      required: true
      type: text
      label:
        en: Replication User
        ja: レプリケーションユーザー
        ms: Pengguna Replikasi
        pt-br: Usuário de Replicação
        ru: Пользователь репликации
        ko: 복제 사용자
        zh-Hant: 複製使用者
        zh: 复制用户
    - default: replica
      envKey: REPLICATION_PASSWORD
      random: true
      required: true
      type: password
      label:
        en: Replication Password
        ja: レプリケーションパスワード
        ms: <PERSON><PERSON> Replikasi
        pt-br: Senha de Replicação
        ru: Пароль репликации
        ko: 복제 비밀번호
        zh-Hant: 複製密碼
        zh: 复制密码
    - default: 5432
      envKey: PANEL_APP_PORT_HTTP
      label:
        en: Port
        ja: ポート
        ms: Port
        pt-br: Porta
        ru: Порт
        ko: 포트
        zh-Hant: 埠
        zh: 端口
      required: true
      rule: paramPort
      type: number
