lua_shared_dict waf_req_count 10m;
lua_shared_dict waf 200m;
lua_shared_dict waf_block_ip 20m;
lua_shared_dict waf_limit 10m;
lua_shared_dict waf_sql 300m;
lua_shared_dict waf_locks 1m;

lua_code_cache on;
lua_package_path "/usr/local/openresty/1pwaf/?.lua;/usr/local/openresty/1pwaf/lib/?.lua;;";
init_by_lua_file  /usr/local/openresty/1pwaf/init.lua;
access_by_lua_file /usr/local/openresty/1pwaf/waf.lua;
log_by_lua_file /usr/local/openresty/1pwaf/log_and_traffic.lua;
init_worker_by_lua_file /usr/local/openresty/1pwaf/worker.lua;
