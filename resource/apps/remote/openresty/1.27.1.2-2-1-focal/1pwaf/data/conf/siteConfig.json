{"waf": {"state": "on", "mode": "protection"}, "args": {"state": "on", "type": "args", "code": 403, "action": "deny"}, "defaultUaBlack": {"type": "defaultUaBlack", "state": "on", "code": 403, "action": "deny"}, "cookie": {"state": "on", "type": "cookie", "code": 403, "action": "deny"}, "geoRestrict": {"state": "off", "rules": [], "code": 403, "action": "deny", "type": "geoRestrict", "res": "geo"}, "xss": {"state": "on", "type": "xss", "code": 403, "action": "deny"}, "sql": {"state": "on", "type": "sql", "code": 403, "action": "deny"}, "cc": {"state": "on", "type": "cc", "rule": "cc", "tokenTimeOut": 1800, "threshold": 200, "duration": 10, "action": "deny", "ipBlock": "on", "ipBlockTime": 600, "mode": "uri"}, "fileExt": {"state": "off", "action": "deny", "code": 403, "type": "fileExtCheck"}, "header": {"state": "on", "type": "header", "code": 403, "action": "deny"}, "defaultUrlBlack": {"type": "defaultUrlBlack", "state": "on", "code": 403, "action": "deny"}, "methodWhite": {"type": "methodW<PERSON>e", "state": "on", "code": 444, "action": "deny"}, "cdn": {"state": "off"}, "strict": {"state": "off", "type": "strict", "code": 403, "action": "deny"}, "app": {"state": "off", "type": "app", "action": "allow", "rule": ""}}