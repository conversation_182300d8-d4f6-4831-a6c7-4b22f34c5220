<!DOCTYPE html>
<html lang="en">
<head>
<title>5s</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<style>
#loadingContainer { position: absolute; top: 50%%; left: 50%%; transform: translate(-50%%, -50%%); display: flex; align-items: center; justify-content: center; flex-direction: column; background: #e8e8e8; width: 300px; height: 100px; border: 2px solid #e8e8e8; }
#loadingText { font-size: 18px; margin-top: 10px; }
#loadingSuccess { display: none; font-size: 24px; color: #7ac23c; margin-top: 10px; }
.loadingSpinner { border: 4px solid rgba(0, 0, 0, 0.1); border-top: 4px solid #7ac23c; border-radius: 50%%; width: 20px; height: 20px; animation: spin 1s linear infinite; margin-top: 10px; }
@keyframes spin { 0%% { transform: rotate(0deg); } 100%% { transform: rotate(360deg); } }
@media screen and (max-width: 768px) { #loadingContainer { width: 90%%; height: auto;padding: 20px;}  #loadingText {font-size: 16px;}  #loadingSuccess {font-size: 20px;}  .loadingSpinner {width: 30px;height: 30px;border-width: 5px;}  }
</style>
</head>
<body>
<div id="loadingContainer">
    <div id="loadingText">正在验证...</div>
    <div id="loadingSuccess">验证成功</div>
    <div class="loadingSpinner"></div>
</div>
<script type="text/javascript" src="/5s_check_%s-%s-%s-.js"></script>
</body>
</html>
