{"name": "Halo", "state": "on", "rules": [{"type": "whiteUrl", "pattern": "contain", "values": ["/apis/api.console.halo.run/v1alpha1/posts", "/api/v1alpha1/configmaps/system", "/apis/api.console.halo.run/v1alpha1/singlepage", "/apis/api.console.halo.run/v1alpha1/attachments/upload", "/apis/api.console.halo.run/v1alpha1/attachments"], "check": {"type": "cookie", "pattern": "eq", "values": ["SESSION"]}}]}