services:
  redis:
    image: redis:8.0.3
    restart: always
    container_name: ${CONTAINER_NAME}
    networks:
      - 1panel-network
    ports:
      - ${PANEL_APP_PORT_HTTP}:6379
    command: redis-server /etc/redis/redis.conf ${PANEL_REDIS_ROOT_PASSWORD:+--requirepass "${PANEL_REDIS_ROOT_PASSWORD}"}
    volumes:
      - ./data:/data
      - ./conf/redis.conf:/etc/redis/redis.conf
      - ./logs:/logs
    labels:
      createdBy: "Apps"
networks:
  1panel-network:
    external: true