services:
  mysql-master:
    image: bitnami/mysql:8.4
    container_name: ${CONTAINER_NAME}
    restart: always
    environment:
      MYSQL_REPLICATION_MODE: master
      MYSQL_REPLICATION_USER: ${REPLICATION_USER}
      MYSQL_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${PANEL_DB_ROOT_PASSWORD}
      MYSQL_AUTHENTICATION_PLUGIN: mysql_native_password
    networks:
      - 1panel-network
    ports:
      - ${PANEL_APP_PORT_HTTP}:3306
    volumes:
      - ./data/:/bitnami/mysql/data
      - ./conf/my.cnf:/opt/bitnami/mysql/conf/my.cnf
      - /etc/localtime:/etc/localtime:ro
    labels:
      createdBy: "Apps"
    healthcheck:
      test: [ 'CMD', '/opt/bitnami/scripts/mysql/healthcheck.sh' ]
      interval: 15s
      timeout: 5s
      retries: 6
networks:
  1panel-network:
    external: true
