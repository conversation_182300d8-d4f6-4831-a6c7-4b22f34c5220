services:
  redis-master:
    image: bitnami/redis:8.0
    restart: always
    container_name: ${CONTAINER_NAME}
    networks:
      - 1panel-network
    ports:
      - ${PANEL_APP_PORT_HTTP}:6379
    volumes:
      - ./data:/bitnami/redis/data
      - ./conf:/opt/bitnami/redis/etc.default
      - ./logs:/opt/bitnami/redis/logs
    environment: 
      REDIS_REPLICATION_MODE: master
      REDIS_PASSWORD: ${PANEL_REDIS_ROOT_PASSWORD}
    labels:
      createdBy: "Apps"
networks:
  1panel-network:
    external: true