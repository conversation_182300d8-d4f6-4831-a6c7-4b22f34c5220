#!/bin/bash

# ===================================================================
# OpenResty IPv4 HTTP正向代理配置脚本 (简化优化版)
# 作者: <PERSON>ro AI Assistant
# 版本: 1.0 Final
# ===================================================================

# --- 请在这里配置您的参数 ---
HAVE_IPV6=true  # 是否启用IPv6支持，默认false
CONTAINER_NAME="1Panel-openresty-AArB"
PROXY_PASSWORD="Yyh275822746Xyz"
PROXY_PORT="18888"
OPENRESTY_PATH="/opt/1panel/apps/openresty/openresty"  # OpenResty配置文件路径
SERVER_IP=$(hostname -I | awk '{print $1}')  # 自动获取服务器IPv4地址

# --- IP地址发现函数 ---

# 获取所有公网IPv4地址
get_ipv4_addresses() {
    local ipv4_list=""
    
    # 方法1: 通过外部服务获取公网IPv4
    for service in "ip.sb" "ipinfo.io/ip" "icanhazip.com" "ident.me" "ifconfig.me"; do
        local ipv4=$(curl -4 -s --connect-timeout 3 --max-time 8 "$service" 2>/dev/null | head -1 | tr -d '\r\n')
        if [[ $ipv4 =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            if [ -z "$ipv4_list" ]; then
                ipv4_list="$ipv4"
            elif [[ "$ipv4_list" != *"$ipv4"* ]]; then
                ipv4_list="$ipv4_list,$ipv4"
            fi
        fi
    done
    
    # 方法2: 保底方法 - 获取本地网络接口的公网IP
    if [ -z "$ipv4_list" ]; then
        echo "⚠️  外部服务获取IPv4失败，使用保底方法" >&2
        for ip in $(hostname -I 2>/dev/null); do
            if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]] && [[ $ip != "127.0.0.1" ]]; then
                # 跳过明显的内网IP，但保留可能的公网IP
                if [[ $ip =~ ^(192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[01])\.) ]]; then
                    continue
                fi
                if [ -z "$ipv4_list" ]; then
                    ipv4_list="$ip"
                else
                    ipv4_list="$ipv4_list,$ip"
                fi
            fi
        done
        
        # 如果还是没有，使用第一个可用的IP（包括内网IP）
        if [ -z "$ipv4_list" ]; then
            for ip in $(hostname -I 2>/dev/null); do
                if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]] && [[ $ip != "127.0.0.1" ]]; then
                    ipv4_list="$ip"
                    break
                fi
            done
        fi
    fi
    
    echo "$ipv4_list"
}

# 获取所有公网IPv6地址
get_ipv6_addresses() {
    if [ "$HAVE_IPV6" != "true" ]; then
        echo ""
        return
    fi
    
    local ipv6_list=""
    
    # 方法1: 通过外部服务获取公网IPv6
    for service in "ip.sb" "ipv6.icanhazip.com" "ident.me" "ifconfig.me"; do
        local ipv6=$(curl -6 -s --connect-timeout 3 --max-time 8 "$service" 2>/dev/null | head -1 | tr -d '\r\n')
        if [[ $ipv6 =~ ^[0-9a-fA-F:]+$ ]] && [[ $ipv6 != "::1" ]] && [[ $ipv6 != fe80:* ]]; then
            if [ -z "$ipv6_list" ]; then
                ipv6_list="$ipv6"
            elif [[ "$ipv6_list" != *"$ipv6"* ]]; then
                ipv6_list="$ipv6_list,$ipv6"
            fi
        fi
    done
    
    # 方法2: 保底方法 - 获取本地网络接口的IPv6地址
    if [ -z "$ipv6_list" ] && command -v ip >/dev/null 2>&1; then
        echo "⚠️  外部服务获取IPv6失败，使用保底方法" >&2
        for ip in $(ip -6 addr show scope global 2>/dev/null | grep inet6 | awk '{print $2}' | cut -d'/' -f1); do
            if [[ $ip != "::1" ]] && [[ $ip != fe80:* ]] && [[ $ip =~ ^[0-9a-fA-F:]+$ ]]; then
                if [ -z "$ipv6_list" ]; then
                    ipv6_list="$ip"
                else
                    ipv6_list="$ipv6_list,$ip"
                fi
            fi
        done
    fi
    
    echo "$ipv6_list"
}

# --- 脚本主体 ---
echo "=========================================="
echo "🚀 OpenResty IPv4+IPv6 HTTP正向代理配置"
echo "=========================================="

# 1. 发现和验证IP地址
echo "1. 发现和验证IP地址..."
echo "   正在检测IPv4地址..."
IPV4_ADDRESSES=$(get_ipv4_addresses)

if [ "$HAVE_IPV6" = "true" ]; then
    echo "   正在检测IPv6地址..."
    IPV6_ADDRESSES=$(get_ipv6_addresses)
else
    IPV6_ADDRESSES=""
fi

# 严格验证：必须有IPv4地址
if [ -z "$IPV4_ADDRESSES" ]; then
    echo "❌ 错误: 未找到IPv4地址"
    exit 1
fi

# 严格验证：如果启用IPv6但没找到IPv6地址，直接报错退出
if [ "$HAVE_IPV6" = "true" ] && [ -z "$IPV6_ADDRESSES" ]; then
    echo "❌ 错误: 启用了IPv6支持但未找到IPv6地址"
    exit 1
fi

echo "✅ 发现的IPv4地址: $IPV4_ADDRESSES"
if [ "$HAVE_IPV6" = "true" ] && [ -n "$IPV6_ADDRESSES" ]; then
    echo "✅ 发现的IPv6地址: $IPV6_ADDRESSES"
    echo "✅ IPv6负载均衡: 已启用"
    
    # 显示负载均衡策略
    ipv4_count=$(echo "$IPV4_ADDRESSES" | tr ',' '\n' | wc -l)
    ipv6_count=$(echo "$IPV6_ADDRESSES" | tr ',' '\n' | wc -l)
    echo "ℹ️  负载均衡策略: IPv4($ipv4_count个) ⟷ IPv6($ipv6_count个) 交替使用"
else
    echo "ℹ️  IPv6负载均衡: 已禁用，仅使用IPv4"
fi

# 2. 检查容器状态
echo "2. 检查容器状态..."
if ! docker ps | grep -q "${CONTAINER_NAME}"; then
    echo "❌ 错误: 容器 ${CONTAINER_NAME} 未运行"
    exit 1
fi
echo "✅ 容器运行正常"

# 3. 清理冲突配置
echo "3. 清理冲突配置..."
rm -f "${OPENRESTY_PATH}/conf/default/"*proxy*.conf* 2>/dev/null
rm -f "${OPENRESTY_PATH}/conf/conf.d/"*proxy*.conf* 2>/dev/null
docker exec "${CONTAINER_NAME}" rm -f /usr/local/openresty/nginx/conf/conf.d/*proxy*.conf 2>/dev/null
echo "✅ 冲突配置已清理"

# 4. 创建密码文件
echo "4. 创建密码文件..."
htpasswd -cb "${OPENRESTY_PATH}/conf/default/proxy_passwords" proxy "${PROXY_PASSWORD}"
echo "✅ 密码文件创建成功"

# 5. 创建代理配置
echo "5. 创建代理配置..."

# 生成IP池配置
IPV4_ARRAY=(${IPV4_ADDRESSES//,/ })
if [ "$HAVE_IPV6" = "true" ] && [ -n "$IPV6_ADDRESSES" ]; then
    IPV6_ARRAY=(${IPV6_ADDRESSES//,/ })
else
    IPV6_ARRAY=()
fi

# 创建包含负载均衡的配置
tee "${OPENRESTY_PATH}/conf/default/ipv4_proxy.conf" > /dev/null << EOF
# IPv4+IPv6 HTTP正向代理配置 - 负载均衡版
server {
    listen 0.0.0.0:18888;
    server_name ipv4_ipv6_proxy;
    
    # DNS解析器 (支持IPv4和IPv6)
    resolver ******* ******* [2001:4860:4860::8888] [2001:4860:4860::8844] valid=300s;
    resolver_timeout 10s;
    
    # 日志配置 - 记录负载均衡信息
    access_log /var/log/nginx/proxy_access.log;
    error_log /var/log/nginx/proxy_error.log;
    
    # 初始化负载均衡状态
    init_by_lua_block {
        -- IP池配置
        local ipv4_list = {$(printf '"%s",' "${IPV4_ARRAY[@]}" | sed 's/,$//')}
        local ipv6_list = {$(if [ ${#IPV6_ARRAY[@]} -gt 0 ]; then printf '"%s",' "${IPV6_ARRAY[@]}" | sed 's/,$//'; fi)}
        
        -- 全局负载均衡状态
        ngx.shared.lb_state:set("current_type", "ipv4")
        ngx.shared.lb_state:set("ipv4_index", 1)
        ngx.shared.lb_state:set("ipv6_index", 1)
        ngx.shared.lb_state:set("use_ipv6", $([ "$HAVE_IPV6" = "true" ] && echo "true" || echo "false"))
        
        -- 存储IP列表
        for i, ip in ipairs(ipv4_list) do
            ngx.shared.lb_state:set("ipv4_" .. i, ip)
        end
        ngx.shared.lb_state:set("ipv4_count", #ipv4_list)
        
        for i, ip in ipairs(ipv6_list) do
            ngx.shared.lb_state:set("ipv6_" .. i, ip)
        end
        ngx.shared.lb_state:set("ipv6_count", #ipv6_list)
        
        ngx.log(ngx.INFO, "负载均衡初始化完成 - IPv4: " .. #ipv4_list .. "个, IPv6: " .. #ipv6_list .. "个")
    }
    
    # 代理逻辑
    location / {
        access_by_lua_block {
            -- 获取代理认证头
            local proxy_auth = ngx.var.http_proxy_authorization
            if not proxy_auth then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                ngx.say("Proxy Authentication Required")
                return ngx.exit(407)
            end
            
            -- 解析认证
            local auth_type, auth_data = string.match(proxy_auth, "(%w+)%s+(.+)")
            if auth_type ~= "Basic" then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                return ngx.exit(407)
            end
            
            -- 验证凭据
            local decoded = ngx.decode_base64(auth_data)
            if not decoded then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                return ngx.exit(407)
            end
            
            local username, password = string.match(decoded, "([^:]+):(.+)")
            if username ~= "proxy" or password ~= "${PROXY_PASSWORD}" then
                ngx.header["Proxy-Authenticate"] = 'Basic realm="HTTP Proxy Auth"'
                ngx.status = 407
                return ngx.exit(407)
            end
            
            -- 验证目标主机
            if not ngx.var.http_host then
                ngx.status = 400
                return ngx.exit(400)
            end
            
            -- 负载均衡IP选择
            local function select_outbound_ip()
                local use_ipv6 = ngx.shared.lb_state:get("use_ipv6")
                local current_type = ngx.shared.lb_state:get("current_type")
                local ipv4_count = ngx.shared.lb_state:get("ipv4_count") or 0
                local ipv6_count = ngx.shared.lb_state:get("ipv6_count") or 0
                
                if use_ipv6 and ipv6_count > 0 then
                    -- IPv4和IPv6交替使用
                    if current_type == "ipv4" then
                        -- 选择IPv4
                        local ipv4_index = ngx.shared.lb_state:get("ipv4_index") or 1
                        local selected_ip = ngx.shared.lb_state:get("ipv4_" .. ipv4_index)
                        
                        -- 更新索引（轮询）
                        local next_index = (ipv4_index % ipv4_count) + 1
                        ngx.shared.lb_state:set("ipv4_index", next_index)
                        ngx.shared.lb_state:set("current_type", "ipv6")
                        
                        return selected_ip, "ipv4"
                    else
                        -- 选择IPv6（随机）
                        local random_index = math.random(1, ipv6_count)
                        local selected_ip = ngx.shared.lb_state:get("ipv6_" .. random_index)
                        ngx.shared.lb_state:set("current_type", "ipv4")
                        
                        return selected_ip, "ipv6"
                    end
                else
                    -- 仅使用IPv4
                    local ipv4_index = ngx.shared.lb_state:get("ipv4_index") or 1
                    local selected_ip = ngx.shared.lb_state:get("ipv4_" .. ipv4_index)
                    
                    -- 更新索引（轮询）
                    local next_index = (ipv4_index % ipv4_count) + 1
                    ngx.shared.lb_state:set("ipv4_index", next_index)
                    
                    return selected_ip, "ipv4"
                end
            end
            
            -- 选择出站IP
            local outbound_ip, ip_type = select_outbound_ip()
            if outbound_ip then
                ngx.var.outbound_ip = outbound_ip
                ngx.var.ip_type = ip_type
                ngx.log(ngx.INFO, "选择出站IP: " .. outbound_ip .. " (类型: " .. ip_type .. ")")
            else
                ngx.log(ngx.ERR, "无法选择出站IP")
                ngx.status = 500
                return ngx.exit(500)
            end
        }
        
        # 使用选择的IP进行代理转发
        proxy_bind \$outbound_ip;
        proxy_pass http://\$http_host\$request_uri;
        proxy_set_header Host \$http_host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header Proxy-Authorization "";
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # HTTP设置
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_buffering off;
        proxy_redirect off;
    }
    
    # 健康检查
    location = /health {
        return 200 "IPv4+IPv6 Proxy OK";
        add_header Content-Type text/plain;
    }
    
    # 负载均衡状态查询
    location = /lb-status {
        access_by_lua_block {
            local current_type = ngx.shared.lb_state:get("current_type")
            local ipv4_count = ngx.shared.lb_state:get("ipv4_count") or 0
            local ipv6_count = ngx.shared.lb_state:get("ipv6_count") or 0
            local use_ipv6 = ngx.shared.lb_state:get("use_ipv6")
            
            ngx.say("负载均衡状态:")
            ngx.say("当前类型: " .. (current_type or "unknown"))
            ngx.say("IPv4数量: " .. ipv4_count)
            ngx.say("IPv6数量: " .. ipv6_count)
            ngx.say("IPv6启用: " .. tostring(use_ipv6))
        }
    }
}


EOF

# 创建共享内存配置
tee "${OPENRESTY_PATH}/conf/default/lua_shared.conf" > /dev/null << 'EOF'
# Lua共享内存配置
lua_shared_dict lb_state 1m;
EOF

echo "✅ 代理配置创建成功"

# 6. 验证并重载配置
echo "6. 验证并重载配置..."
if docker exec "${CONTAINER_NAME}" openresty -t; then
    docker exec "${CONTAINER_NAME}" openresty -s reload
    echo "✅ 配置重载成功"
else
    echo "❌ 配置验证失败"
    exit 1
fi

# 6. 等待服务启动
sleep 3

# 7. 基本功能测试
echo "7. 基本功能测试..."

# 健康检查
if curl -s -m 5 "http://${SERVER_IP}:${PROXY_PORT}/health" | grep -q "IPv4 Proxy OK"; then
    echo "✅ 健康检查通过"
else
    echo "⚠️  健康检查失败"
fi

# 认证测试
AUTH_TEST=$(curl -s -m 10 -x "http://proxy:${PROXY_PASSWORD}@${SERVER_IP}:${PROXY_PORT}" "http://httpbin.org/ip" 2>/dev/null)
if echo "$AUTH_TEST" | grep -q "origin"; then
    echo "🎉 代理功能测试成功!"
    echo "   外部IP: $(echo "$AUTH_TEST" | jq -r .origin 2>/dev/null || echo "$AUTH_TEST")"
else
    echo "⚠️  代理功能测试失败"
fi

echo ""
echo "=========================================="
echo "🎉 IPv4 HTTP正向代理配置完成!"
echo "=========================================="
echo ""
echo "📋 代理信息:"
echo "   地址: ${SERVER_IP}:${PROXY_PORT}"
echo "   用户名: proxy"
echo "   密码: ${PROXY_PASSWORD}"
echo ""
echo "🚀 使用示例:"
echo "   curl -x http://proxy:${PROXY_PASSWORD}@${SERVER_IP}:${PROXY_PORT} http://httpbin.org/ip"
echo ""
echo "📁 配置文件:"
echo "   ${OPENRESTY_PATH}/conf/default/ipv4_proxy.conf"
echo ""
echo "📊 日志监控:"
echo "   docker exec ${CONTAINER_NAME} tail -f /var/log/nginx/ipv4_proxy_access.log"
echo ""
echo "=========================================="
echo "✅ 配置完成!"
echo "=========================================="